# Bani代码生成规则文档

## 1. 代码生成模板概述

代码生成基于Velocity模板引擎，模板文件位于：
`bani-service/bani-gen/src/main/resources/vm/java/`

主要模板文件：

- `controller.java.vm`: 控制器层模板
- `service.java.vm`: 服务接口层模板
- `serviceImpl.java.vm`: 服务实现层模板
- `mapper.java.vm`: Mapper接口模板
- `domain.java.vm`: 领域模型模板
- `bo.java.vm`: 业务对象模板
- `vo.java.vm`: 值对象模板

## 2. 通用变量

所有模板共享以下变量：

- `${packageName}`: 包名
- `${ClassName}`: 类名(首字母大写)
- `${className}`: 类名(首字母小写)
- `${functionName}`: 功能描述
- `${moduleName}`: 模块名
- `${businessName}`: 业务名
- `${author}`: 作者
- `${datetime}`: 日期时间
- `${permissionPrefix}`: 权限前缀
- `${table.crud}`: 是否为CRUD表标识
- `${table.tree}`: 是否为树形表标识
- `${pkColumn}`: 主键列信息
    - `${pkColumn.javaField}`: 主键字段名
    - `${pkColumn.javaType}`: 主键类型
    - `${pkColumn.columnName}`: 主键列名

## 3. 各层模板规则

### 3.1 Controller层

文件：`controller.java.vm`

功能：

- 生成标准的Spring MVC控制器
- 包含以下标准接口：
    - 分页查询
    - 导出
    - 详情
    - 新增
    - 修改
    - 删除

特点：

- 集成了权限控制
- 包含日志记录
- 支持重复提交校验

### 3.2 Service接口层

文件：`service.java.vm`

功能：

- 定义服务接口
- 包含以下标准方法：
    - queryById: 根据ID查询
    - queryPageList: 分页查询
    - queryList: 列表查询
    - insertByBo: 新增
    - updateByBo: 修改
    - deleteWithValidByIds: 批量删除

### 3.3 Service实现层

文件：`serviceImpl.java.vm`

功能：

- 实现服务接口
- 使用MyBatis Plus进行数据库操作
- 包含buildQueryWrapper方法动态构建查询条件
- 使用Mapstruct进行对象转换

特点：

- 实现了标准的CRUD操作
- 包含数据校验逻辑
- 支持分页查询
- 方法实现规范统一

## 4. 使用说明

1. 配置代码生成器参数：
    - 设置包名、类名、功能描述等基本信息
    - 配置表信息(CRUD/树形表)
    - 设置主键信息

2. 运行代码生成器

3. 生成的代码结构：

```
${packageName}
├── controller
├── domain
├── mapper
├── service
│   ├── impl
├── domain
│   ├── bo
│   └── vo
```

4. 生成后需要手动处理的部分：

- 数据校验逻辑(validEntityBeforeSave方法)
- 业务特定的校验逻辑
- 自定义查询条件
