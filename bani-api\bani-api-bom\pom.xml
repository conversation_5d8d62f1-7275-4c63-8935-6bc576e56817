<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gzhuxn</groupId>
    <artifactId>bani-api-bom</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <description>
        bani-api-bom api依赖项
    </description>

    <properties>
        <revision>2.2.1</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 系统接口 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-api-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 资源服务接口 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-api-resource</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 交友服务接口 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-api-personals</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
