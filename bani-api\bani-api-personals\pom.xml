<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-api</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>bani-api-personals</artifactId>
    <description>
        bani-api-personals 交友服务接口模块
    </description>

    <dependencies>

        <!-- bani Common Core-->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-base</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-json</artifactId>
        </dependency>

    </dependencies>

</project>
