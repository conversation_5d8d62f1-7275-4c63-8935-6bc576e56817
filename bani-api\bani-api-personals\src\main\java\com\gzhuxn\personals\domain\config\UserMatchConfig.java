package com.gzhuxn.personals.domain.config;

import com.gzhuxn.common.core.utils.ValidatorUtils;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户匹配配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/11 23:38
 */
@Data
public class UserMatchConfig implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 最小年龄
     */
    @Min(value = 18, message = "最小年龄必须大于等于18岁")
    private Integer ageMin;
    /**
     * 最大年龄
     */
    @Min(value = 70, message = "最小年龄必须小于等于70岁")
    private Integer ageMax;

    /**
     * 最小身高
     */
    @Min(value = 140, message = "最小身高必须大于等于140cm")
    private Integer highMin;
    /**
     * 最大身高
     */
    @Min(value = 220, message = "最大身高必须小于等于220cm")
    private Integer highMax;
    /**
     * 要求收入
     */
    private Integer requireRevenue;
    /**
     * 推荐城市
     * 1不限、2同城优先、3只要同城
     */
    private Integer recommendCity;
    /**
     * 推荐籍贯
     * 1不限、2同乡优先
     */
    private Integer recommendNativePlace;

    public UserMatchConfig() {
        this.ageMin = 18;
        this.ageMax = 70;
        this.highMin = 140;
        this.highMax = 220;
        this.recommendCity = 1;
        this.recommendNativePlace = 1;
    }

    /**
     * 校验
     */
    public void isValid() {
        ValidatorUtils.validate(this);
    }
}
