package com.gzhuxn.personals.domain.order;

import com.gzhuxn.common.base.enums.PayStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/8 11:59
 */
@Data
public class PayResult {
    /**
     * 订单号
     */
    private String no;
    /**
     * 微信支付订单号
     */
    private String payNo;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 支付状态
     */
    private PayStatus status;
}
