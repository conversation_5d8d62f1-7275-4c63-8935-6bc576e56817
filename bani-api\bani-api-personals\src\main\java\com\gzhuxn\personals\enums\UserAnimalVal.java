package com.gzhuxn.personals.enums;

import lombok.Getter;

import java.time.LocalDate;

/**
 * 用户生肖
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/28 10:37
 */
public enum UserAnimalVal {
    /**
     * 鼠
     */
    MONKEY("猴"),
    CHICKEN("鸡"),
    DOG("狗"),
    PIG("猪"),
    RAT("鼠"),
    OX("牛"),
    TIGER("虎"),
    RABBIT("兔"),
    DRAGON("龙"),
    SNAKE("蛇"),
    HORSE("马"),
    GOAT("羊"),
    ;
    @Getter
    private final String value;


    UserAnimalVal(String value) {
        this.value = value;
    }

    /**
     * 根据日期获取生肖
     *
     * @param date 日期
     * @return 生肖
     */
    public static UserAnimalVal of(LocalDate date) {
        return UserAnimalVal.values()[date.getYear() % 12];
    }

    /**
     * 根据日期获取生肖
     *
     * @param date 日期
     * @return 生肖
     */
    public static String ofValue(LocalDate date) {
        return of(date).value;
    }
}
