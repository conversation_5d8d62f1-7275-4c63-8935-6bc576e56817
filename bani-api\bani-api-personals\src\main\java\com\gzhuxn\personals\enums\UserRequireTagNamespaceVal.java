package com.gzhuxn.personals.enums;

import lombok.Getter;

/**
 * 用户择偶标签命名空间枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/28 17:45
 */
@Getter
public enum UserRequireTagNamespaceVal {
    /**
     * 个人信息
     */
    PROFILE("profile"),
    /**
     * 兴趣爱好
     */
    INTEREST("interest"),
    /**
     * 特点
     * 字典：require_trait_val
     */
    TRAIT("trait"),
    /**
     * 恋爱准则
     */
    CRITERION("criterion"),
    ;
    private final String value;

    UserRequireTagNamespaceVal(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
