package com.gzhuxn.personals.enums;

import lombok.Getter;

/**
 * 用户标签个人信息字段枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/28 17:50
 */
@Getter
public enum UserRequireTagProfileKeyVal {
    /**
     * 年龄范围
     */
    ONES("age", "年龄范围"),
    /**
     * 最低身高
     */
    WANT_MARRY("height", "最低身高"),
    /**
     * 学历要求
     * 字典：require_tag_edu
     */
    CAR("edu", "学历要求"),
    /**
     * 收入要求
     * 字典：require_tag_revenue
     */
    REVENUE("revenue", "收入要求"),
    /**
     * 住房要求，多选
     * 字典：require_tag_house
     */
    HOUSE("house", "住房要求"),
    /**
     * 婚况要求
     * 字典：require_tag_marriage
     */
    MARRIAGE("marriage", "婚况要求"),
    /**
     * 接受小孩
     * 字典：require_tag_accept_child
     */
    ACCEPT_CHILD("accept_child", "接受小孩"),
    ;

    private final String value;
    private final String dictType;
    private final String name;

    UserRequireTagProfileKeyVal(String value, String name) {
        this.value = value;
        this.dictType = "require_tag_" + value;
        this.name = name;
    }
}
