package com.gzhuxn.personals.enums;

import cn.hutool.core.date.DateUtil;
import com.gzhuxn.common.core.utils.DateUtils;
import lombok.Getter;

import java.time.LocalDate;

/**
 * 用户星座
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/28 10:37
 */
public enum UserStarVal {
    /**
     * 水瓶座
     */
    AQUARIUS("水瓶座", "1.20-2.18"),
    PISCES("双鱼座", "2.19-3.20"),
    ARIES("白羊座", "3.21-4.19"),
    TAURUS("金牛座", "4.20-5.20"),
    GEMINI("双子座", "5.21-6.21"),
    CANCER("巨蟹座", "6.22-7.22"),
    LEO("狮子座", "7.23-8.22"),
    VIRGO("处女座", "8.23-9.22"),
    LIBRA("天秤座", "9.23-10.23"),
    SCORPIO("天蝎座", "10.24-11.22"),
    SAGITTARIUS("射手座", "11.23-12.21"),
    CAPRICORN("摩羯座", "12.22-1.19");
    @Getter
    private final String value;
    private final String dateRange;

    UserStarVal(String value, String dateRange) {
        this.value = value;
        this.dateRange = dateRange;
    }

    /**
     * 根据日期获取星座
     *
     * @param date 日期
     * @return 星座
     */
    public static UserStarVal of(LocalDate date) {
        String dateStr = DateUtil.format(DateUtils.toDate(date), "MM.dd");
        float dateF = Float.parseFloat(dateStr);
        for (UserStarVal value : values()) {
            String[] dates = value.dateRange.split("-");
            float startF = Float.parseFloat(dates[0]);
            float endF = Float.parseFloat(dates[1]);
            // 摩羯座
            if (CAPRICORN == value) {
                if (dateF >= startF && dateF <= 12.31) {
                    return value;
                }
                if (dateF >= 1.1 && dateF <= endF) {
                    return value;
                }
            } else {
                if (dateF >= startF && dateF <= endF) {
                    return value;
                }
            }
        }
        throw new IllegalArgumentException("Invalid date: " + date);
    }

    /**
     * 根据日期获取星座
     *
     * @param date 日期
     * @return 星座
     */
    public static String ofValue(LocalDate date) {
        return of(date).value;
    }
}
