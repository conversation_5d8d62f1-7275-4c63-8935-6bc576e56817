package com.gzhuxn.personals.enums;

/**
 * 用户标签枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/28 17:45
 */
public enum UserTagNamespaceVal {
    /**
     * 个人信息
     */
    PROFILE("profile"),
    /**
     * 兴趣爱好
     * 字典：user_interest_val
     */
    INTEREST("interest"),
    /**
     * 特点
     */
    TRAIT("trait"),
    /**
     * 恋爱准则
     */
    CRITERION("criterion"),
    ;
    private final String value;

    UserTagNamespaceVal(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
