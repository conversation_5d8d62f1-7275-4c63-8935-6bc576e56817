package com.gzhuxn.personals.enums;

import lombok.Getter;

/**
 * 用户标签个人信息字段枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/28 17:50
 */
@Getter
public enum UserTagProfileKeyVal {
    /**
     * 独生子女
     * 字典：profile_ones_val
     */
    ONES("ones", "独生子女"),
    /**
     * 何时结婚
     * 字典：profile_want_marry_val
     */
    WANT_MARRY("want_marry", "何时结婚"),
    /**
     * 车辆状况
     * 字典：profile_car_val
     */
    CAR("car", "车辆状况"),
    ;

    private final String value;
    private final String dictType;
    private final String name;

    UserTagProfileKeyVal(String value, String name) {
        this.value = value;
        this.dictType = getNamespace().getValue() + "_" + value + "_val";
        this.name = name;
    }

    /**
     * 获取字段命名空间
     */
    public static UserTagNamespaceVal getNamespace() {
        return UserTagNamespaceVal.PROFILE;
    }
}
