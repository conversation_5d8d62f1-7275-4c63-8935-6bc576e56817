package com.gzhuxn.personals.enums.activity;

import lombok.Getter;

/**
 * 审核状态;
 * <p>
 * 0未发起审核、1审核中、2审核通过、3审核被拒绝
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/16 13:34
 */
@Getter
public enum ActivityAuditStatus {
    UNAUDITED(0, "未发起审核"),
    AUDITING(1, "审核中"),
    PASS(2, "审核通过"),
    REFUSE(3, "审核被拒绝");

    private final Integer value;
    private final String name;

    ActivityAuditStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
