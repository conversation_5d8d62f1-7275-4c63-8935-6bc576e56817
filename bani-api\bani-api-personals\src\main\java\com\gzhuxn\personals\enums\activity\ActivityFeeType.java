package com.gzhuxn.personals.enums.activity;

import lombok.Getter;

/**
 * 活动费用类型枚举
 * <p>
 * 1保障费用、10活动费用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/16 18:04
 */
@Getter
public enum ActivityFeeType {
    SAFEGUARD(1, "保障费用"),
    ACTIVITY(10, "活动费用");

    private final Integer value;
    private final String desc;

    ActivityFeeType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
