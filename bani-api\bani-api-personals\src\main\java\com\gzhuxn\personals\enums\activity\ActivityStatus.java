package com.gzhuxn.personals.enums.activity;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 活动状态;
 * <p>
 * 1草稿，2已发布/未开始、3报名中、4报名已结束、10活动进行中、11活动已结束
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/16 13:34
 */
@Getter
public enum ActivityStatus {
    DEL(0, "下架"),
    DRAFT(1, "草稿"),
    PUBLISHED(2, "未开始"),
    ENROLL_START(3, "报名中"),
    ENROLL_END(4, "报名已结束"),
    ACTIVITY_START(10, "活动进行中"),
    ACTIVITY_END(11, "活动已结束");

    private final Integer value;
    private final String desc;

    ActivityStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return status
     */
    public static ActivityStatus of(Integer value) {
        for (ActivityStatus status : ActivityStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new ServiceException("活动状态不存在！");
    }
}
