package com.gzhuxn.personals.enums.activity;

import lombok.Getter;

/**
 * 类型
 * <p>
 * 1相亲会、2聊天、3干饭、4户外、5看展、6运动、7学习、8喝酒、9打游戏、10其他
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/16 13:34
 */
@Getter
public enum ActivityTypeVal {
    QX(1, "相亲会"),
    TX(2, "聊天"),
    GNF(3, "干饭"),
    HW(4, "户外"),
    KZX(5, "看展"),
    YD(6, "运动"),
    XS(7, "学习"),
    JK(8, "喝酒"),
    DY(9, "打游戏"),
    QT(10, "其他");

    private final Integer value;
    private final String name;

    ActivityTypeVal(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
