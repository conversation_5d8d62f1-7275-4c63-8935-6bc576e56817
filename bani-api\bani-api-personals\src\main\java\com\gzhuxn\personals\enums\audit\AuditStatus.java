package com.gzhuxn.personals.enums.audit;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态
 * 0待发起审核、1 待审核、2 通过、3 未通过、11转人工审核
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 17:50
 */
@Getter
@AllArgsConstructor
public enum AuditStatus {
    WAIT_SUBMIT(0, "待提交"),
    DRAFT(1, "草稿"),
    /**
     * 发起审核
     */
    WAIT_AUDIT(2, "待审核"),
    TRANSFER_WAIT_AUDIT(11, "转人工审核"),
    PASS(3, "通过"),
    REJECT(4, "拒绝"),
    ;
    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static AuditStatus of(Integer value) {
        for (AuditStatus status : AuditStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new ServiceException("审核状态不存在");
    }
}
