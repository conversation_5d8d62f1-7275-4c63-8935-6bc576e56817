package com.gzhuxn.personals.enums.audit;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 审核类型
 * <p>
 * 活动
 * 账号信息
 * 动态
 * 评论
 * 101-实名认证、2-学历认证、3-车辆认证、4-房屋认证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:20
 */
@Getter
@NoArgsConstructor
public enum AuditType {
    ACTIVITY(1, "活动"),
    USER_BASE_INFO(2, "账号信息"),
    MOMENT(3, "动态"),
    COMMENT(4, "评论"),

    // 认证申请
    AUTH_APPLY_IDENTITY(101, "实名认证"),
    AUTH_APPLY_EDUCATION(102, "学历认证"),
    AUTH_APPLY_CAR(103, "车辆认证"),
    AUTH_APPLY_HOUSE(104, "房屋认证"),


    // 默认
    DEFAULT(-1, "未知");

    private Integer value;
    private String desc;

    AuditType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 是否是认证申请
     */
    public boolean isAuthApply() {
        return value >= 101 && value < 105;
    }

    /**
     * 根据value获取枚举
     */
    public static AuditType of(Integer value) {
        for (AuditType auditType : AuditType.values()) {
            if (auditType.value.equals(value)) {
                return auditType;
            }
        }
        throw new ServiceException("审核类型不存在");
    }

    /**
     * 获取认证申请类型
     */
    public static AuditType[] getAuditApplyTypes() {
        return new AuditType[]{AUTH_APPLY_IDENTITY, AUTH_APPLY_EDUCATION, AUTH_APPLY_CAR, AUTH_APPLY_HOUSE};
    }
}
