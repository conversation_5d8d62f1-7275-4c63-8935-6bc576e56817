package com.gzhuxn.personals.enums.coin;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 花瓣配置类型
 * <p>
 * 1签到、2新手任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/28 17:25
 */
@Getter
public enum CoinMgType {
    SIGN_IN(1, "签到"),
    NOVICE_TASK(2, "新手任务");

    private final Integer value;
    private final String desc;

    CoinMgType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     */
    public static CoinMgType of(Integer value) {
        for (CoinMgType item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        throw new ServiceException("花瓣配置类型不存在");
    }
}
