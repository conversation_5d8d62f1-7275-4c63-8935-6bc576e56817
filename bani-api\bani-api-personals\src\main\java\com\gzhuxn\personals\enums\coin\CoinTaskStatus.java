package com.gzhuxn.personals.enums.coin;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 花瓣任务状态枚举
 * <p>
 * 0-未完成、1-已完成、2-已领取
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-14
 */
@Getter
public enum CoinTaskStatus {
    /**
     * 未完成
     */
    INCOMPLETE(0, "未完成"),
    /**
     * 已完成
     */
    COMPLETED(1, "已完成"),
    /**
     * 已领取
     */
    CLAIMED(2, "已领取");

    private final Integer value;
    private final String desc;

    CoinTaskStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static CoinTaskStatus of(Integer value) {
        for (CoinTaskStatus status : CoinTaskStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new ServiceException("花瓣任务状态不存在");
    }
}
