package com.gzhuxn.personals.enums.coin;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 花瓣类型
 * <p>
 * 1平台赠送、2可提现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/28 17:25
 */
@Getter
public enum CoinType {
    /**
     * 平台赠送
     */
    GIFT(1, "平台赠送"),

    /**
     * 可提现
     */
    WITHDRAW(2, "可提现"),
    ;

    private final Integer value;
    private final String desc;

    CoinType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     */
    public static CoinType of(Integer value) {
        for (CoinType item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        throw new ServiceException("花瓣类型不存在");
    }
}
