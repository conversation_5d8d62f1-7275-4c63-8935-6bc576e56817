package com.gzhuxn.personals.enums.config;

import com.gzhuxn.common.core.exception.ServiceException;
import com.gzhuxn.common.json.utils.JsonUtils;
import com.gzhuxn.personals.domain.config.UserMatchConfig;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;

/**
 * 账号通知key 枚举
 * <p>
 * 是否发送公众号消息
 * ms_follow  关注消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/10 11:36
 */
@Getter
public enum UserConfigKeyVal {
    MS_FOLLOW("ms_follow", "是否发送公众号消息"),


    USER_MATCH("user_match", "用户匹配") {
        @Override
        public void valid(@NotBlank(message = "用户匹配配置值不能为空") String configValue) {
            UserMatchConfig config = JsonUtils.parseObject(configValue, UserMatchConfig.class);
            config.isValid();
        }
    },
    ;

    private final String value;
    private final String pattern;
    private final String desc;

    UserConfigKeyVal(String value, String desc) {
        // 默认只允许0或1
        this(value, "^[01]$", desc);
    }

    UserConfigKeyVal(String value, String pattern, String desc) {
        this.value = value;
        this.pattern = pattern;
        this.desc = desc;
    }

    /**
     * 是否匹配
     *
     * @param configValue 值
     * @return true/false
     */
    public boolean isValid(String configValue) {
        return configValue.matches(pattern);
    }

    /**
     * 是否匹配
     *
     * @param configValue 值
     */
    public void valid(@NotBlank(message = "配置值不能为空") String configValue) {
        if (!isValid(configValue)) {
            throw new ServiceException(value + ",配置值不合法");
        }
    }

    /**
     * 根据key获取枚举
     *
     * @param configKey key
     * @return 枚举
     */
    public static UserConfigKeyVal of(String configKey) {
        for (UserConfigKeyVal item : values()) {
            if (item.value.equals(configKey)) {
                return item;
            }
        }
        throw new ServiceException(configKey + ",配置key不合法");
    }

}
