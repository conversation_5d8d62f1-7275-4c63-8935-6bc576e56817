package com.gzhuxn.personals.enums.dict;

import lombok.Getter;

import java.util.List;

/**
 * 学历级别
 * 初中及以下	1
 * 高中	2
 * 中专	3
 * 大专	4
 * 本科	5
 * 硕士	6
 * 博士	7
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 14:46
 */
@Getter
public enum UserEdu {
    JUNIOR_HIGH_OR_BELOW(1, "初中及以下"),
    SENIOR_HIGH(2, "高中"),
    VOCATIONAL_SECONDARY(3, "中专"),
    JUNIOR_COLLEGE(4, "大专"),
    BACHELOR(5, "本科"),
    MASTER(6, "硕士"),
    DOCTORAL(7, "博士"),
    ;

    private final int value;
    private final String name;

    UserEdu(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getDictType() {
        return "user_edu";
    }

    /**
     * 获取学历级别大于等于value的学历级别
     *
     * @param minEduLevel 最低学历级别
     * @return List<UserEdu>
     */
    public static List<UserEdu> highThan(UserEdu minEduLevel) {
        return List.of(values()).subList(minEduLevel.ordinal() + 1, values().length);
    }
}
