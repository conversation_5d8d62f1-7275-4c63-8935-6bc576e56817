package com.gzhuxn.personals.enums.dict;

import lombok.Getter;

/**
 * 房子状况
 * 和家人同住	1
 * 已购房	2
 * 租房	3
 * 婚后购房	4
 * 住在单位宿舍	5
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 15:05
 */
@Getter
public enum UserHouse {
    HOUSE_LIVING_WITH_FAMILY(1, "和家人同住"),
    HOUSE_OWNED(2, "已购房"),
    HOUSE_RENT(3, "租房"),
    HOUSE_OWNED_WITH_OPPOSITE(4, "婚后购房"),
    HOUSE_LIVING_IN_UNIT_DORMITORY(5, "住在单位宿舍"),
    ;

    private final int value;
    private final String name;

    UserHouse(int value, String name) {
        this.value = value;
        this.name = name;
    }
}
