package com.gzhuxn.personals.enums.dict;

import lombok.Getter;

/**
 * 用户工作
 * 普工	1
 * 电商行业	2
 * 服务行业	3
 * 美容行业	4
 * 快递行业	5
 * 汽修行业	6
 * 事业单位合同制员工	7
 * 事业编制人员	8
 * 公务员	9
 * 公司高管	10
 * 公司职员	11
 * 银行职员	12
 * 国企职员	13
 * 教师	14
 * 幼师	15
 * 医生	16
 * 护士	17
 * 警察	18
 * 辅警	19
 * IT从业者	20
 * 设计师	21
 * 律师	22
 * 工程师	23
 * 中小型企业老板	24
 * 个体户	25
 * 创业者	26
 * 人事主管	27
 * 财务主管	28
 * 销售经理	29
 * 会计出纳	30
 * 行政人员	31
 * 运营专员	32
 * 销售代表	33
 * 客服	34
 * 快递人员	35
 * 司机	36
 * 厨师	37
 * 摄影师	38
 * 花艺师	39
 * 烘焙师	40
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 14:25
 */
@Getter
public enum UserJob {
    PINGGONG(1, "普通工"),
    DANSHANG(2, "电商行业"),
    FUWU(3, "服务行业"),
    MEIRONG(4, "美容行业"),
    KUAIJIU(5, "快递行业"),
    QIEXU(6, "汽修行业"),
    SHENGYUAN(7, "事业单位合同制员工"),
    SHENGYUANBENZHI(8, "事业编制人员"),
    GONGWUYUAN(9, "公务员"),
    GONGSIGUANXIANG(10, "公司高管"),
    GONGSIZHILI(11, "公司职员"),
    YINXIANGZHILI(12, "银行职员"),
    GONGSIBOSS(13, "公司老板"),
    GEETONG(14, "个体户"),
    QINGYUAN(15, "创业者"),
    RENSHIGUANZHU(27, "人事主管"),
    FINANCEGUANZHU(28, "财务主管"),
    SALESMANAGER(29, "销售经理"),
    ACCOUNTANT(30, "会计出纳"),
    ADMINISTRATOR(31, "行政人员"),
    OPERATION(32, "运营专员"),
    SALESPERSON(33, "销售代表"),
    CUSTOMERSERVICE(34, "客服"),
    EXPRESS(35, "快递人员"),
    DRIVER(36, "司机"),
    COOK(37, "厨师"),
    PHOTOGRAPHER(38, "摄影师"),
    HAIRDRESSER(39, "花艺师"),
    BAKERY(40, "烘焙师");
    private final int value;
    private final String name;

    UserJob(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getDictType() {
        return "user_job";
    }
}
