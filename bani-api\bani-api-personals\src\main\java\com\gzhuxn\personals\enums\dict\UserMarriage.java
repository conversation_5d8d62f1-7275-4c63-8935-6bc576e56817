package com.gzhuxn.personals.enums.dict;

import lombok.Getter;

import java.util.List;

/**
 * 个人婚姻状态
 * 未婚	1
 * 离异未育	2
 * 离异带孩	3
 * 离异孩跟对方	4
 * 丧偶	5
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 15:04
 */
@Getter
public enum UserMarriage {
    UNMARRIED(1, "未婚"),
    DIVORCED_NO_BABY(2, "离异未育"),
    DIVORCED_WITH_BABY(3, "离异带孩"),
    DIVORCED_WITH_BABY_AND_OPPOSITE(4, "离异孩跟对方"),
    WIDOWED(5, "丧偶");

    private final int value;
    private final String name;

    UserMarriage(int value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 离异
     *
     * @return list
     */
    public static List<UserMarriage> divorced() {
        return List.of(DIVORCED_NO_BABY, DIVORCED_WITH_BABY, DIVORCED_WITH_BABY_AND_OPPOSITE, WIDOWED);
    }
}
