package com.gzhuxn.personals.enums.dict;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 收入状况
 * 3千以下	1
 * 3千~5千	2
 * 5千~8千	3
 * 8千~1.2万	4
 * 1.2万~2万	5
 * 2万~5万	6
 * 5万以上	7
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 14:48
 */
@Getter
public enum UserRevenue {
    THREE_THOUSAND_BELOW(1, "3千以下"),
    THREE_THOUSAND_TO_FIVE_THOUSAND(2, "3千~5千"),
    FIVE_THOUSAND_TO_EIGHT_THOUSAND(3, "5千~8千"),
    EIGHT_THOUSAND_TO_ONE_TWO_THOUSAND(4, "8千~1.2万"),
    ONE_TWO_THOUSAND_TO_TWO_THOUSAND(5, "1.2万~2万"),
    TWO_THOUSAND_TO_FIVE_THOUSAND(6, "2万~5万"),
    FIVE_THOUSAND_ABOVE(7, "5万以上");

    private final int value;
    private final String name;

    UserRevenue(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getDictType() {
        return "user_revenue";
    }

    /**
     * 获取高于某个收入状况的集合
     *
     * @param userRevenue 收入状况
     * @return 收入状况集合
     */
    public static List<UserRevenue> lowerThan(UserRevenue userRevenue) {
        if (null == userRevenue) {
            return Collections.emptyList();
        }
        List<UserRevenue> revenues = new ArrayList<>(UserRevenue.values().length);
        for (UserRevenue value : UserRevenue.values()) {
            if (value.value >= userRevenue.value) {
                revenues.add(value);
            }
        }
        return revenues;
    }
}
