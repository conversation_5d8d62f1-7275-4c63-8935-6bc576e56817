package com.gzhuxn.personals.enums.dict.requiretag;

import lombok.Getter;

/**
 * 匹配条件：是否接受孩子
 * <p>
 * 不限	-1
 * 无所谓	1
 * 不接受	2
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:21
 */
@Getter
public enum RequireTagAcceptChild {
    NO(-1, "不限"),
    ANY(1, "无所谓"),
    REJECT(2, "不接受"),
    ;

    private final int value;
    private final String name;

    RequireTagAcceptChild(int value, String name) {
        this.value = value;
        this.name = name;
    }
}
