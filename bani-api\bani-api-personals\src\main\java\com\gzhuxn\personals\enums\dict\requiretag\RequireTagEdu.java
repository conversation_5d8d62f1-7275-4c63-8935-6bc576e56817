package com.gzhuxn.personals.enums.dict.requiretag;

import com.gzhuxn.personals.enums.dict.UserEdu;
import lombok.Getter;

import java.util.List;

import static com.gzhuxn.personals.enums.dict.UserEdu.highThan;

/**
 * 学历要求
 * <p>
 * 不限	-1
 * 高中及以上	1
 * 大专及以上	2
 * 本科及以上	3
 * 硕士及以上	4
 * 博士及以上	5
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:10
 */
@Getter
public enum RequireTagEdu {
    NO(-1, "不限"),
    SENIOR_HIGH_OR_ABOVE(1, "高中及以上", highThan(UserEdu.SENIOR_HIGH)),
    JUNIOR_COLLEGE_OR_ABOVE(2, "大专及以上", highThan(UserEdu.JUNIOR_COLLEGE)),
    BACHELOR_OR_ABOVE(3, "本科及以上", highThan(UserEdu.BACHELOR)),
    MASTER_OR_ABOVE(4, "硕士及以上", highThan(UserEdu.MASTER)),
    DOCTORAL_OR_ABOVE(5, "博士及以上", highThan(UserEdu.DOCTORAL));

    private final int value;
    private final String name;
    private final List<UserEdu> edus;

    RequireTagEdu(int value, String name) {
        this(value, name, null);
    }

    RequireTagEdu(int value, String name, List<UserEdu> edus) {
        this.value = value;
        this.name = name;
        this.edus = edus;
    }
}
