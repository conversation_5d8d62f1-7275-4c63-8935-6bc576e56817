package com.gzhuxn.personals.enums.dict.requiretag;

import com.gzhuxn.personals.enums.dict.UserHouse;
import lombok.Getter;

/**
 * 住房要求
 * <p>
 * 不限	-1
 * 和家人同住	1
 * 已购房	2
 * 租房	3
 * 婚后购房	4
 * 住在单位宿舍	5
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:15
 */
@Getter
public enum RequireTagHouse {
    NO(-1, "不限"),
    HOUSE_LIVING_WITH_FAMILY(1, "和家人同住", UserHouse.HOUSE_LIVING_WITH_FAMILY),
    HOUSE_OWNED(2, "已购房", UserHouse.HOUSE_OWNED),
    HOUSE_RENT(3, "租房", UserHouse.HOUSE_RENT),
    HOUSE_OWNED_WITH_OPPOSITE(4, "婚后购房", UserHouse.HOUSE_OWNED_WITH_OPPOSITE),
    HOUSE_LIVING_IN_UNIT_DORMITORY(5, "住在单位宿舍", UserHouse.HOUSE_LIVING_IN_UNIT_DORMITORY);

    private final int value;
    private final String name;
    private final UserHouse house;

    RequireTagHouse(int value, String name) {
        this(value, name, null);
    }

    RequireTagHouse(int value, String name, UserHouse house) {
        this.value = value;
        this.name = name;
        this.house = house;
    }
}
