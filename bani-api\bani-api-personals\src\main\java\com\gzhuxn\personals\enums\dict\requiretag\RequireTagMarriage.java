package com.gzhuxn.personals.enums.dict.requiretag;

import com.gzhuxn.personals.enums.dict.UserMarriage;
import lombok.Getter;

import java.util.List;

/**
 * 婚况要求
 * <p>
 * 不限	-1
 * 仅限未婚	1
 * 仅限离异	2
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:19
 */
@Getter
public enum RequireTagMarriage {
    NO(-1, "不限"),
    ONLY_UNMARRIED(1, "仅限未婚", List.of(UserMarriage.UNMARRIED)),
    ONLY_DIVORCED(2, "仅限离异", UserMarriage.divorced()),
    ;
    private final int value;
    private final String name;
    private final List<UserMarriage> marriages;

    RequireTagMarriage(int value, String name) {
        this(value, name, null);
    }

    RequireTagMarriage(int value, String name, List<UserMarriage> marriages) {
        this.value = value;
        this.name = name;
        this.marriages = marriages;
    }
}
