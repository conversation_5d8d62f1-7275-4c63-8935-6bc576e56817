package com.gzhuxn.personals.enums.dict.requiretag;

import com.gzhuxn.personals.enums.dict.UserRevenue;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;

import java.util.List;

/**
 * 收入要求
 * <p>
 * 不限	-1
 * 3千以上	1
 * 5千以上	2
 * 8千以上	3
 * 1.2万以上	4
 * 2万以上	5
 * 5万以上	6
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:12
 */
@Getter
public enum RequireTagRevenue {
    NO(-1, "不限"),
    ABOVE_THREE_THOUSAND(1, "3千以上", UserRevenue.THREE_THOUSAND_TO_FIVE_THOUSAND),
    ABOVE_FIVE_THOUSAND(2, "5千以上", UserRevenue.FIVE_THOUSAND_TO_EIGHT_THOUSAND),
    ABOVE_EIGHT_THOUSAND(3, "8千以上", UserRevenue.EIGHT_THOUSAND_TO_ONE_TWO_THOUSAND),
    ABOVE_TWELVE_THOUSAND(4, "1.2万以上", UserRevenue.ONE_TWO_THOUSAND_TO_TWO_THOUSAND),
    ABOVE_TWENTY_THOUSAND(5, "2万以上", UserRevenue.TWO_THOUSAND_TO_FIVE_THOUSAND),
    ABOVE_FIFTY_THOUSAND(6, "5万以上", UserRevenue.FIVE_THOUSAND_ABOVE),
    ;

    private final int value;
    private final String name;
    private final List<UserRevenue> revenues;

    RequireTagRevenue(int value, String name) {
        this(value, name, null);
    }

    RequireTagRevenue(int value, String name, UserRevenue lowRevenue) {
        this.value = value;
        this.name = name;
        this.revenues = UserRevenue.lowerThan(lowRevenue);
    }

    /**
     * 根据value获取枚举
     *
     * @param value
     * @return
     */
    public static RequireTagRevenue of(@NotNull(message = "value不能为空") Integer value) {
        for (RequireTagRevenue requireTagRevenue : values()) {
            if (requireTagRevenue.value == value) {
                return requireTagRevenue;
            }
        }
        throw new IllegalArgumentException("RequireTagRevenue value值非法");
    }
}
