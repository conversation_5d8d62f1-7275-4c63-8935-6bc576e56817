package com.gzhuxn.personals.enums.dict.tag;

import lombok.Getter;

/**
 * 用户想结婚
 * <p>
 * 认同闪婚	1
 * 一年内结婚	2
 * 两年内结婚	3
 * 三年内结婚	4
 * 时机成熟就结婚	5
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:08
 */
@Getter
public enum UserTagWantMarry {
    ONE(1, "认同闪婚"),
    TWO(2, "一年内结婚"),
    THREE(3, "两年内结婚"),
    FOUR(4, "三年内结婚"),
    FIVE(5, "时机成熟就结婚");

    private final int value;
    private final String name;

    UserTagWantMarry(int value, String name) {
        this.value = value;
        this.name = name;
    }
}
