package com.gzhuxn.personals.enums.group;

import lombok.Getter;

/**
 * 管理员标识;1群主、2管理员、3成员
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 23:43
 */
@Getter
public enum GroupAdminFlag {
    OWNER(1, "群主"),
    ADMIN(2, "管理员"),
    MEMBER(3, "成员");

    private final Integer value;
    private final String desc;

    GroupAdminFlag(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
