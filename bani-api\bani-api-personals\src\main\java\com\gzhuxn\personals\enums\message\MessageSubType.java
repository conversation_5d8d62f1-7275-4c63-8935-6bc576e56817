package com.gzhuxn.personals.enums.message;

import lombok.Getter;

/**
 * 消息子类型
 * <p>
 * 系统通知：
 * 1001公开、1002私有
 * <p>
 * 与我相关：
 * 2001关注我的 2002点赞
 * 2010微信申请
 * 2020送礼物
 * <p>
 * 2030我的问答
 * 2040活动报名、2041活动开始
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/10 9:12
 */
@Getter
public enum MessageSubType {
    /**
     * 系统通知：
     */
    PUBLIC(1001, "公开"),
    PRIVATE(1002, "私有"),

    // -------------- 相关消息 ----------------------------
    /**
     * 与我相关：
     */
    RELATED_FOLLOW(2001, "关注我的"),
    RELATED_LIKE(2002, "点赞"),
    RELATED_WECHAT_APPLY(2010, "微信申请"),
    RELATED_GIFT(2020, "收到礼物"),
    RELATED_MY_QUESTION(2030, "收到问答"),

    // 活动相关
    RELATED_ACTIVITY_ENROLL(2041, "活动报名"),
    RELATED_ACTIVITY_START(2042, "活动开始"),

    // 认证相关
    RELATED_AUTH_APPLY_SUCCESS(2051, "认证成功"),
    RELATED_AUTH_APPLY_FAIL(2052, "认证失败"),

    // 审核相关
    RELATED_AUDIT_SUCCESS(2061, "审核通过"),
    RELATED_AUDIT_REJECT(2062, "审核拒绝"),

    ;

    private final Integer value;
    private final String desc;

    MessageSubType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
