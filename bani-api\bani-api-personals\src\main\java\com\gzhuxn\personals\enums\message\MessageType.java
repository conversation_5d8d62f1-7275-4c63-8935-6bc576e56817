package com.gzhuxn.personals.enums.message;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 消息类型枚举
 * <p>
 * 1系统通知、2与我相关、3私聊消息、4群聊消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/10 9:05
 */
@Getter
public enum MessageType {
    /**
     * 系统通知
     */
    SYSTEM(1, "系统通知"),
    RELATED(2, "与我相关"),

    /**
     * 聊天消息
     */
    MSG_USER(3, "私聊消息"),
    MSG_GROUP(4, "群聊消息"),
    ;

    private final Integer value;
    private final String desc;

    MessageType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static MessageType of(Integer value) {
        for (MessageType item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        throw new ServiceException("未知消息类型：" + value);
    }
}
