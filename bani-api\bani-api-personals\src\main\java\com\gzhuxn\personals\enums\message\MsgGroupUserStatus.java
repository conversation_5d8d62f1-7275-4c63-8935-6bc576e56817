package com.gzhuxn.personals.enums.message;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 群聊组用户状态
 * 类型：1正常、2退群、3被移除群聊、4退群/被移除且删除群聊
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/1 23:08
 */
@Getter
@AllArgsConstructor
public enum MsgGroupUserStatus {
    NORMAL(1, "正常"),
    QUIT(2, "退群"),
    REMOVED(3, "被移除群聊"),
    QUIT_REMOVED(4, "退群/被移除且删除群聊");

    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     */
    public static MsgGroupUserStatus of(Integer value) {
        for (MsgGroupUserStatus item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        throw new ServiceException("群聊用户类型不存在" + value);
    }
}
