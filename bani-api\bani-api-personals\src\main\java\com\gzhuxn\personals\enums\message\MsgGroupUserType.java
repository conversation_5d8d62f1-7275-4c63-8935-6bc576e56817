package com.gzhuxn.personals.enums.message;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 群聊组用户类型枚举
 * 类型：1群主/超管、2管理员、3群组成员不能为空
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/1 23:08
 */
@Getter
@AllArgsConstructor
public enum MsgGroupUserType {
    OWNER(1, "群主"),
    ADMIN(2, "管理员"),
    USER(3, "群组成员"),
    ;

    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     */
    public static MsgGroupUserType of(Integer value) {
        for (MsgGroupUserType item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        throw new ServiceException("群聊用户类型不存在" + value);
    }
}
