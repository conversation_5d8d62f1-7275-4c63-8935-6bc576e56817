package com.gzhuxn.personals.enums.user;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 使用状态枚举
 * <p>
 * 0未使用、1已使用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-14
 */
@Getter
public enum UseStatus {
    /**
     * 未使用
     */
    UNUSED(0, "未使用"),
    /**
     * 已使用
     */
    USED(1, "已使用");

    private final Integer value;
    private final String desc;

    UseStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static UseStatus of(Integer value) {
        for (UseStatus status : UseStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new ServiceException("使用状态不存在");
    }
}
