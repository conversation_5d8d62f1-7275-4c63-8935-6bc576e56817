package com.gzhuxn.personals.enums.user;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户注销状态枚举
 * <p>
 * 0注销中、1注销成功
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Getter
@AllArgsConstructor
public enum UserLogoffStatus {
    /**
     * 注销中
     */
    PROCESSING(0, "注销中"),
    /**
     * 注销成功
     */
    SUCCESS(1, "注销成功");

    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static UserLogoffStatus of(Integer value) {
        for (UserLogoffStatus status : UserLogoffStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new ServiceException("用户注销状态不存在");
    }
}
