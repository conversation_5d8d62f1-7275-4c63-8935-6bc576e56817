package com.gzhuxn.personals.enums.user.account;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账户历史操作类型
 * <p>
 * -1扣减、1增加
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/3 17:25
 */
@Getter
@AllArgsConstructor
public enum AccountHistoryOpType {
    /**
     * 扣减
     */
    DEDUCT(-1, "扣减"),

    /**
     * 增加
     */
    ADD(1, "增加");

    /**
     * 值
     */
    private final Integer value;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据value获取枚举
     */
    public static AccountHistoryOpType of(Integer value) {
        for (AccountHistoryOpType item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        throw new ServiceException("花瓣操作类型不存在");
    }

}
