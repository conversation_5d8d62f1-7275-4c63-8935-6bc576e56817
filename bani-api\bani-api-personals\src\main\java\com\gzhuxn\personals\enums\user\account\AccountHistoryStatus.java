package com.gzhuxn.personals.enums.user.account;

import lombok.Getter;

import java.util.Objects;

/**
 * 账户历史状态
 * <p>
 * 0待扣除、1正常
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/3 17:25
 */
@Getter
public enum AccountHistoryStatus {
    /**
     * 待扣除
     */
    LOCK(0, "待扣除"),
    NORMAL(1, "正常");

    private final Integer value;
    private final String desc;

    AccountHistoryStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     */
    public static AccountHistoryStatus of(Integer value) {
        return AccountHistoryStatus.values()[value];
    }

    /**
     * 是否锁定
     */
    public boolean isLock() {
        return Objects.equals(this.value, LOCK.getValue());
    }
}
