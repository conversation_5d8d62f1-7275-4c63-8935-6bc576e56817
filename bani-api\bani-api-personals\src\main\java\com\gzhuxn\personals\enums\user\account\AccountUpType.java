package com.gzhuxn.personals.enums.user.account;

import lombok.Getter;

/**
 * 账号变更类型枚举类
 * <p>
 * 业务类型;
 * <p>
 * 收入：
 * 1充值、2礼物、3微信申请、4问答
 * <p>
 * 支出：
 * 101提现、102礼物、103微信申请、104问答
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 16:04
 */
@Getter
public enum AccountUpType {
    /**
     * 收入
     */
    RECHARGE(1, "充值"),
    GIFT(2, "礼物"),
    WX_APPLY(3, "微信申请"),
    QUESTION(4, "问答"),
    AUTH_REWARD(5, "认证奖励"),





    /**
     * 支出
     */
    WITHDRAW(101, "提现"),
    GIFT_OUT(102, "礼物", AccountUpType.GIFT),
    WX_APPLY_OUT(103, "微信申请", AccountUpType.WX_APPLY),
    QUESTION_OUT(104, "问答", AccountUpType.QUESTION);

    /**
     * 值
     */
    private final Integer value;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 对方变更类型
     */
    private final AccountUpType oppositeUpType;

    AccountUpType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
        this.oppositeUpType = null;
    }

    AccountUpType(Integer value, String desc, AccountUpType oppositeUpType) {
        this.value = value;
        this.desc = desc;
        this.oppositeUpType = oppositeUpType;
    }

    /**
     * 是否是收入
     *
     * @return true是，false不是
     */
    public boolean isIncome() {
        return this.value < 100;
    }
}
