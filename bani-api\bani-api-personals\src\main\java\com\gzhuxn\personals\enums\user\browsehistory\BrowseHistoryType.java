package com.gzhuxn.personals.enums.user.browsehistory;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 浏览历史类型
 * <p>
 * 1用户主页、2活动、3用户动态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 21:48
 */
@Getter
public enum BrowseHistoryType {
    /**
     * 用户主页
     */
    USER_HOME(1),
    /**
     * 活动
     */
    ACTIVITY(2),
    /**
     * 用户动态
     */
    USER_DYNAMIC(3);

    /**
     * 值
     */
    private final Integer value;

    BrowseHistoryType(Integer value) {
        this.value = value;
    }

    public static BrowseHistoryType of(Integer value) {
        for (BrowseHistoryType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new ServiceException("浏览历史类型不存在");
    }
}
