package com.gzhuxn.personals.enums.user.browsehistory;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 浏览历史类型
 * <p>
 * 1浏览我的、2我浏览的
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 21:48
 */
@Getter
public enum UserHomeBrowseHistoryType {
    /**
     * 浏览我的
     */
    BROWSE_ME(1),

    /**
     * 我浏览的
     */
    ME_BROWSE(2),
    ;

    /**
     * 值
     */
    private final Integer value;

    UserHomeBrowseHistoryType(Integer value) {
        this.value = value;
    }

    public static UserHomeBrowseHistoryType of(Integer value) {
        for (UserHomeBrowseHistoryType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new ServiceException("浏览历史类型不存在");
    }
}
