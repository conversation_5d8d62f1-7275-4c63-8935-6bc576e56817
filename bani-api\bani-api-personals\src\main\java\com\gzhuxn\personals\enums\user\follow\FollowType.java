package com.gzhuxn.personals.enums.user.follow;

import lombok.Getter;

/**
 * 关注类型枚举
 * <p>
 * 1-关注用户 2-关注俱乐部 3-关注活动 4-关注话题
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 15:16
 */
@Getter
public enum FollowType {
    /**
     * 关注用户
     */
    USER(1),
    /**
     * 关注俱乐部
     */
    CLUB(2),
    /**
     * 关注活动
     */
    ACTIVITY(3),
    /**
     * 关注话题
     */
    TOPIC(4);

    private final Integer value;

    FollowType(Integer value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static FollowType of(Integer value) {
        for (FollowType item : FollowType.values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
