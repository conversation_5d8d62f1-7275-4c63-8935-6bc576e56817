package com.gzhuxn.personals.enums.user.follow;

import lombok.Getter;

/**
 * 关注类型枚举
 * <p>
 * 1关注我的、2我关注的、3互相关注的
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 15:16
 */
@Getter
public enum FollowUserType {
    /**
     * 我关注的
     */
    I_FOLLOW(1),
    /**
     * 关注我的
     */
    FOLLOW_ME(2),
    /**
     * 互相关注的
     */
    I_FOLLOW_ME(3),

    ;

    private final Integer value;

    FollowUserType(Integer value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static FollowUserType of(Integer value) {
        for (FollowUserType item : FollowUserType.values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
