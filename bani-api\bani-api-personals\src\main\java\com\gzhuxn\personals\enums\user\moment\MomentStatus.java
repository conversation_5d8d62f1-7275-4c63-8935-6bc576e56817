package com.gzhuxn.personals.enums.user.moment;

import lombok.Getter;

/**
 * 动态状态
 * <p>
 * 0下架、1草稿、2已发布
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/17 16:59
 */
@Getter
public enum MomentStatus {
    DEL(0, "下架"),
    DRAFT(1, "草稿"),
    PUBLISHED(2, "已发布"),
    ;

    private final Integer code;
    private final String message;

    MomentStatus(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
