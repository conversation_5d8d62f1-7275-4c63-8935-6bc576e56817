package com.gzhuxn.personals.enums.user.question;

import lombok.Getter;

/**
 * 问题申请状态
 * <p>
 * 回答状态;：0待发起、1待回答、2已回答、3已拒绝
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/3 11:46
 */
@Getter
public enum QuestionApplyStatus {
    /**
     * 待发起
     */
    WAIT_START(0, "待发起"),

    WAIT_ANSWER(1, "待回答"),

    ANSWERED(2, "已回答") {
        @Override
        public boolean isClose() {
            return true;
        }
    },

    REFUSED(3, "已拒绝") {
        @Override
        public boolean isClose() {
            return true;
        }
    };

    private final Integer value;
    private final String desc;

    QuestionApplyStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 是否关闭
     *
     * @return 是否关闭
     */
    public boolean isClose() {
        return false;
    }

    /**
     * 获取问题申请状态
     *
     * @param value 值
     * @return 问题申请状态
     */
    public static QuestionApplyStatus of(Integer value) {
        for (QuestionApplyStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知问题申请状态：" + value);
    }
}
