package com.gzhuxn.personals.enums.user.require;

import lombok.Getter;

/**
 * 要求个人信息-
 * <p>
 * 学历:
 * -1不限
 * 1高中及以上
 * 2大专及以上
 * 3本科及以上
 * 4硕士及以上
 * 5博士及以上
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/19 15:35
 */
@Getter
public enum UserRequireTagEdu {
    UNLIMITED(-1, "不限"),
    HIGH_SCHOOL(1, "高中及以上"),
    COLLEGE(2, "大专及以上"),
    BACHELOR(3, "本科及以上"),
    MASTER(4, "硕士及以上"),
    DOCTOR(5, "博士及以上"),
    ;

    private final Integer value;
    private final String name;

    UserRequireTagEdu(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
