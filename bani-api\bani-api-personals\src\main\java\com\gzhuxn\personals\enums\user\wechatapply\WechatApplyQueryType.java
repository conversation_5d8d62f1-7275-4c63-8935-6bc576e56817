package com.gzhuxn.personals.enums.user.wechatapply;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 微信申请类型
 * <p>
 * 1申请我的、2我申请的
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 21:48
 */
@Getter
public enum WechatApplyQueryType {
    /**
     * 申请我的
     */
    APPLY_ME(1),

    /**
     * 我申请的
     */
    ME_APPLY(2),
    ;

    /**
     * 值
     */
    private final Integer value;

    WechatApplyQueryType(Integer value) {
        this.value = value;
    }

    public static WechatApplyQueryType of(Integer value) {
        for (WechatApplyQueryType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new ServiceException("微信申请类型不存在");
    }
}
