package com.gzhuxn.personals.enums.user.wechatapply;

import lombok.Getter;

/**
 * 微信申请状态枚举
 * <p>
 * 0待发起、1申请中、2已通过、3已拒绝、4过期未处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/5 23:52
 */
@Getter
public enum WechatApplyStatus {
    /**
     * 待发起
     */
    WAIT_START(0, "待发起"),
    APPLYING(1, "申请中"),
    APPLIED(2, "已通过"),
    REFUSED(3, "已拒绝"),
    EXPIRED(4, "过期未处理"),
    ;

    private final Integer value;
    private final String desc;

    WechatApplyStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static WechatApplyStatus of(Integer value) {
        for (WechatApplyStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return null;
    }

}
