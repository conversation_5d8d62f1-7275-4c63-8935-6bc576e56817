package com.gzhuxn.personals.enums.withdraw;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 提现审核状态
 * 1发起申请、2审核通过、3打回补充、4拒绝
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Getter
@AllArgsConstructor
public enum WithdrawAuditStatus {
    /**
     * 发起申请
     */
    APPLY(1, "发起申请"),
    
    /**
     * 审核通过
     */
    PASS(2, "审核通过"),
    
    /**
     * 打回补充
     */
    RETURN(3, "打回补充"),
    
    /**
     * 拒绝
     */
    REJECT(4, "拒绝");

    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static WithdrawAuditStatus of(Integer value) {
        for (WithdrawAuditStatus status : WithdrawAuditStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new ServiceException("提现审核状态不存在");
    }

    /**
     * 是否可以修改
     *
     * @return true-可以修改，false-不可以修改
     */
    public boolean canModify() {
        return this == APPLY || this == RETURN;
    }

    /**
     * 是否已完成审核
     *
     * @return true-已完成，false-未完成
     */
    public boolean isCompleted() {
        return this == PASS || this == REJECT;
    }
}
