package com.gzhuxn.personals.enums.withdraw;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 提现汇款状态
 * 1待汇款、2已汇款
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Getter
@AllArgsConstructor
public enum WithdrawRemitStatus {
    /**
     * 待汇款
     */
    WAIT_REMIT(1, "待汇款"),
    
    /**
     * 已汇款
     */
    REMITTED(2, "已汇款");

    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static WithdrawRemitStatus of(Integer value) {
        for (WithdrawRemitStatus status : WithdrawRemitStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new ServiceException("提现汇款状态不存在");
    }

    /**
     * 是否已汇款
     *
     * @return true-已汇款，false-未汇款
     */
    public boolean isRemitted() {
        return this == REMITTED;
    }
}
