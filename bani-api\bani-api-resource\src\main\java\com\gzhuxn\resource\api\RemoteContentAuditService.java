package com.gzhuxn.resource.api;

import com.gzhuxn.resource.api.domain.RemoteCheckIdentity;
import com.gzhuxn.resource.api.domain.RemoteCheckImage;
import com.gzhuxn.resource.api.domain.RemoteCheckText;

import java.util.List;

/**
 * 内容审核服务
 *
 * <AUTHOR>
 */
public interface RemoteContentAuditService {
    /**
     * 敏感词配置key
     */
    String SENSITIVE_WORDS_CONFIG_KEY = "content.audit.sensitive.words";

    /**
     * check text
     *
     * @param texts 内容
     */
    RemoteCheckText checkText(String... texts);

    /**
     * check image
     *
     * @param bytes 图片文件
     */
    RemoteCheckImage checkImage(List<byte[]> bytes);

    /**
     * check identity
     *
     * @param name   姓名
     * @param idCard 身份证号
     * @param ossId  人脸图片Id
     */
    RemoteCheckIdentity checkIdentity(String name, String idCard, Long ossId);
}
