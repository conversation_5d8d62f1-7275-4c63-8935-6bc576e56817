package com.gzhuxn.resource.api;

import com.gzhuxn.common.core.exception.ServiceException;
import com.gzhuxn.resource.api.domain.RemoteFile;

import java.util.List;
import java.util.Map;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
public interface RemoteFileService {

    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    RemoteFile upload(String name, String originalFilename, String contentType, byte[] file) throws ServiceException;

    /**
     * 通过ossId查询对应的url
     *
     * @param ossIds ossId串逗号分隔
     * @return url串逗号分隔
     */
    String selectUrlByIds(String ossIds);

    /**
     * 通过ossId查询列表
     *
     * @param ossIds ossId串逗号分隔
     * @return 列表
     */
    List<RemoteFile> selectByIds(String ossIds);

    /**
     * 通过ossId查询列表
     *
     * @param ossIds ossIds
     * @return 列表
     */
    List<RemoteFile> selectByIds(List<Long> ossIds);

    /**
     * 通过ossId查询列表
     *
     * @param ossIds ossIds
     * @return map
     */
    Map<Long, RemoteFile> selectMapByIds(List<Long> ossIds);
}
