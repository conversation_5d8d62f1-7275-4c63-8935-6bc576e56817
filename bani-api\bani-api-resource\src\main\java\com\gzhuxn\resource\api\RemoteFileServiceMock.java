package com.gzhuxn.resource.api;

import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.resource.api.domain.RemoteFile;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 文件服务(降级处理)
 *
 * <AUTHOR>
 */
@Slf4j
public class RemoteFileServiceMock implements RemoteFileService {

    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    @Override
    public RemoteFile upload(String name, String originalFilename, String contentType, byte[] file) {
        log.warn("服务调用异常 -> 降级处理");
        return null;
    }

    /**
     * 通过ossId查询对应的url
     *
     * @param ossIds ossId串逗号分隔
     * @return url串逗号分隔
     */
    @Override
    public String selectUrlByIds(String ossIds) {
        log.warn("服务调用异常 -> 降级处理");
        return StringUtils.EMPTY;
    }

    /**
     * 通过ossId查询列表
     *
     * @param ossIds ossId串逗号分隔
     * @return 列表
     */
    @Override
    public List<RemoteFile> selectByIds(String ossIds) {
        log.warn("服务调用异常 -> 降级处理");
        return List.of();
    }

    @Override
    public List<RemoteFile> selectByIds(List<Long> ossIds) {
        log.warn("服务调用异常 -> 降级处理");
        return List.of();
    }

    @Override
    public Map<Long, RemoteFile> selectMapByIds(List<Long> ossIds) {
        log.warn("服务调用异常 -> 降级处理");
        return Map.of();
    }

}
