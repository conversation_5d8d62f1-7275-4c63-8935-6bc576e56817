package com.gzhuxn.resource.api.domain;

import lombok.NoArgsConstructor;

/**
 * 身份证认证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/7 11:21
 */
@lombok.Data
@NoArgsConstructor
public class RemoteCheckIdentity {
    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCard;
    /*
     * 是否通过
     */
    private Boolean pass;

    /**
     * 原因
     */
    private String reason;

    public RemoteCheckIdentity(String name, String idCard) {
        this.name = name;
        this.idCard = idCard;
        this.pass = false;
    }

    /**
     * 是否通过
     */
    public boolean isPass() {
        return Boolean.TRUE.equals(pass);
    }
}
