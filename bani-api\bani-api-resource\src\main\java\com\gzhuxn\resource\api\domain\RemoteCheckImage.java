package com.gzhuxn.resource.api.domain;

import com.gzhuxn.resource.api.enums.CheckImageLabel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 图片信息审核结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RemoteCheckImage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 元素列表
     */
    private List<Element> elements;

    public RemoteCheckImage(List<Element> elements) {
        this.elements = elements;
    }

    /**
     * 是否通过
     */
    public boolean isPass() {
        return elements.stream().allMatch(Element::isPass);
    }

    /**
     * 标签列表
     */
    public List<CheckImageLabel> labels() {
        return elements.stream().flatMap(e -> e.getResults().stream()).map(Result::getLabel).toList();
    }

    /**
     * 违规提示
     */
    public String violationTips() {
        return labels().stream().filter(l -> CheckImageLabel.NORMAL != l).distinct().map(CheckImageLabel::getDesc).collect(Collectors.joining("、"));
    }

    /**
     * 元素
     */
    @Data
    @NoArgsConstructor
    public static class Element implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 元素索引
         */
        private int index;
        /**
         * 结果列表
         */
        private List<Result> results;

        public Element(List<Result> results) {
            this.results = results;
        }

        /**
         * 是否通过
         */
        public boolean isPass() {
            return results.stream().allMatch(Result::getPass);
        }
    }

    /**
     * 结果
     */
    @Data
    public static class Result implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 是否通过
         */
        private Boolean pass;
        /**
         * 标签
         */
        private CheckImageLabel label;
        /**
         * 置信度
         */
        private Float rate;
    }
}
