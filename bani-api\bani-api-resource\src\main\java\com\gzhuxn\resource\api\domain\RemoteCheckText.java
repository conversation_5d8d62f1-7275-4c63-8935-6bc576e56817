package com.gzhuxn.resource.api.domain;

import cn.hutool.core.collection.CollUtil;
import com.gzhuxn.resource.api.enums.CheckTextLabel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文本信息审核结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RemoteCheckText implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 元素列表
     */
    private List<Element> elements;

    public RemoteCheckText(List<Element> elements) {
        this.elements = elements;
    }

    /**
     * 是否通过
     */
    public boolean isPass() {
        return CollUtil.isEmpty(elements) || elements.stream().allMatch(Element::isPass);
    }

    /**
     * 标签列表
     */
    public List<CheckTextLabel> labels() {
        return elements.stream().flatMap(e -> e.getResults().stream()).map(Result::getLabel).toList();
    }

    /**
     * 违规提示
     */
    public String failMag() {
        return labels().stream().filter(l -> CheckTextLabel.NORMAL != l).distinct().map(CheckTextLabel::getDesc).collect(Collectors.joining("、"));
    }

    /**
     * 元素
     */
    @Data
    @NoArgsConstructor
    public static class Element implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 元素索引
         */
        private int index;
        /**
         * 结果列表
         */
        private List<Result> results;

        public Element(Result result) {
            this.results = new ArrayList<>(1);
            this.results.add(result);
        }

        public Element(List<Result> results) {
            this.results = results;
        }

        /**
         * 是否通过
         */
        public boolean isPass() {
            return results.stream().allMatch(Result::getPass);
        }
    }

    /**
     * 结果
     */
    @Data
    public static class Result implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 是否通过
         */
        private Boolean pass;
        /**
         * 标签
         */
        private CheckTextLabel label;
        /**
         * 置信度
         */
        private Float rate;

        /**
         * 内容列表
         */
        private List<String> contents;
    }
}
