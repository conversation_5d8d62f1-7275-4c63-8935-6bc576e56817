package com.gzhuxn.resource.api.enums;

import lombok.Getter;

/**
 * 验证文本标签
 * <p>
 * 场景：porn -图片智能鉴黄：适用于图片涉及色情、低俗内容检测
 * sexy（性感图片）
 * porn（色情图片）
 * <p>
 * 场景：terrorism -图片敏感内容识别：适用于图片涉及敏感事件、暴力、武器、恐怖、血腥、爆炸等内容识别。图片风险人物识别：适用于图片涉及敏感人物、明星的识别。
 * bloody（血腥）
 * explosion（爆炸烟光）
 * outfit（特殊装束）
 * logo（特殊标识）
 * weapon（武器）
 * politics（敏感内容）
 * violence（打斗）
 * crowd（聚众）
 * parade（游行）
 * carcrash（车祸现场）
 * flag（旗帜）
 * location（地标）
 * drug（涉毒）
 * gamble（赌博）
 * others（其他）
 * <p>
 * 场景：ad -图片广告识别：适用于图片识别广告内容，包括文字广告、图片广告、视频广告等。
 * politics（文字含敏感内容）
 * porn（文字含涉黄内容）
 * abuse（文字含辱骂内容）
 * terrorism（文字含涉恐内容）
 * contraband（文字含违禁内容）
 * spam（文字含其他垃圾内容）
 * npx（牛皮癣广告）
 * qrcode（包含二维码）
 * programCode（包含小程序码）
 * ad（其他广告）
 * <p>
 * 场景：live 0图片不良场景识别：适用于图片中涉及毒品、赌博、画中画等内容的识别。
 * meaningless（无意义图片）
 * PIP（画中画）
 * smoking（吸烟）
 * drivelive（车内直播）
 * drug（涉毒）
 * gamble（赌博）
 * <p>
 * 场景：logo -图片 Logo 识别：适用于图片中含有台标、水印、商标等内容的检测。
 * TV（带有管控 logo 的图片）
 * trademark（商标）
 * <p>
 * normal: 正常
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 16:09
 */
@Getter
public enum CheckImageLabel {
    SEXY("sexy", "性感图片"),
    PORN("porn", "色情图片"),

    BLOODY("bloody", "血腥"),
    EXPLOSION("explosion", "爆炸烟光"),
    OUTFIT("outfit", "特殊装束"),
    LOGO("logo", "特殊标识"),
    WEAPON("weapon", "武器"),
    POLITICS("politics", "敏感内容"),
    VIOLENCE("violence", "打斗"),
    CROWD("crowd", "聚众"),
    PARADE("parade", "游行"),
    CARCRASH("carcrash", "车祸现场"),
    FLAG("flag", "旗帜"),
    LOCATION("location", "地标"),
    DRUG("drug", "涉毒"),
    GAMBLE("gamble", "赌博"),
    OTHERS("others", "其他"),

    POLITICS_TEXT("politics", "文字含敏感"),
    PORN_TEXT("porn", "文字含涉黄"),
    ABUSE_TEXT("abuse", "文字含辱骂"),
    TERRORISM_TEXT("terrorism", "文字含涉恐"),
    CONTRABAND_TEXT("contraband", "文字含违禁"),
    SPAM_TEXT("spam", "文字含其他垃圾内容"),
    NPX_TEXT("npx", "牛皮癣广告"),
    QRCODE("qrcode", "包含二维码"),
    PROGRAMCODE("programCode", "包含小程序码"),
    AD("ad", "其他广告"),

    MEANINGLESS("meaningless", "无意义图片"),
    PIP("PIP", "画中画"),
    SMOKING("smoking", "吸烟"),
    DRIVE_LIVE("drivelive", "车内直播"),
    DRUG_LIVE("drug", "涉毒直播"),
    GAMBLE_LIVE("gamble", "赌博直播"),

    TV("TV", "带有管控 logo 的图片"),
    TRADMARK("trademark", "商标"),

    NORMAL("normal", "正常");
    private final String label;
    private final String desc;

    CheckImageLabel(String label, String desc) {
        this.label = label;
        this.desc = desc;
    }

    /**
     * 根据标签获取枚举
     *
     * @param label 标签
     * @return 枚举
     */
    public static CheckImageLabel of(String label) {
        for (CheckImageLabel checkImageLabel : CheckImageLabel.values()) {
            if (checkImageLabel.getLabel().equals(label)) {
                return checkImageLabel;
            }
        }
        return null;
    }
}
