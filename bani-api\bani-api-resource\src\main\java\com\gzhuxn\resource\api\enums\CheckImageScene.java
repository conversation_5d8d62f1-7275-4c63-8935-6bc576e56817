package com.gzhuxn.resource.api.enums;

import lombok.Getter;

/**
 * 验证图片场景
 * <p>
 * porn：图片智能鉴黄：适用于图片涉及色情、低俗内容检测。
 * terrorism：图片敏感内容识别：适用于图片涉及敏感事件、暴力、武器、恐怖、血腥、爆炸等内容识别。图片风险人物识别：适用于图片涉及敏感人物、明星的识别。
 * ad：图片垃圾广告识别：适用于图片中含有广告信息的识别，特別是针对于类似牛皮癣的文字广告有较好的识别效果。
 * live:图片不良场景识别：适用于图片中涉及毒品、赌博、画中画等内容的识别。
 * logo:图片 Logo 识别：适用于图片中含有台标、水印、商标等内容的检测。
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 16:09
 */
@Getter
public enum CheckImageScene {
    PORN("porn", "鉴黄"),
    TERRORISM("terrorism", "敏感内容"),
    AD("ad", "垃圾广告"),
    LIVE("live", "不良场景"),
    LOGO("logo", "Logo");

    private final String scene;
    private final String desc;

    CheckImageScene(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    /**
     * 根据场景获取枚举
     *
     * @param scene 场景
     * @return 枚举
     */
    public static CheckImageScene of(String scene) {
        for (CheckImageScene value : CheckImageScene.values()) {
            if (value.getScene().equals(scene)) {
                return value;
            }
        }
        return null;
    }
}
