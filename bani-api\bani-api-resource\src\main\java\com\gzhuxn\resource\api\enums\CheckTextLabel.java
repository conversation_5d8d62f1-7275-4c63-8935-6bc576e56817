package com.gzhuxn.resource.api.enums;

import lombok.Getter;

/**
 * 验证文本标签
 * <p>
 * spam：文字垃圾
 * politics：文字敏感
 * abuse：文字辱骂
 * terrorism：文字暴恐
 * porn：文字鉴黄
 * flood：文字灌水
 * contraband：文字违禁
 * ad：文字广告
 * normal: 正常
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 16:09
 */
@Getter
public enum CheckTextLabel {
    SPAM("spam", "文字垃圾"),
    POLITICS("politics", "文字敏感"),
    ABUSE("abuse", "文字辱骂"),
    TERRORISM("terrorism", "文字暴恐"),
    PORN("porn", "文字鉴黄"),
    FLOOD("flood", "文字灌水"),
    CONTRABAND("contraband", "文字违禁"),
    AD("ad", "文字广告"),
    NORMAL("normal", "正常");

    private final String label;
    private final String desc;

    CheckTextLabel(String label, String desc) {
        this.label = label;
        this.desc = desc;
    }

    /**
     * 根据label获取枚举
     *
     * @param label 标签
     * @return 枚举
     */
    public static CheckTextLabel of(String label) {
        for (CheckTextLabel value : CheckTextLabel.values()) {
            if (value.label.equals(label)) {
                return value;
            }
        }
        return NORMAL;
    }

}
