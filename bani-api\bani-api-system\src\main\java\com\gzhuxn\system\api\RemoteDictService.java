package com.gzhuxn.system.api;

import com.gzhuxn.system.api.domain.vo.RemoteDictDataVo;

import java.util.List;

/**
 * 字典服务
 *
 * <AUTHOR>
 */
public interface RemoteDictService {

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    List<RemoteDictDataVo> selectDictDataByType(String dictType);

    /**
     * 根据字典类型和字典值查询字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    String selectLabelByType(String dictType, String dictValue);

}
