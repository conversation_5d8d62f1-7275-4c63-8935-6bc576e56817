package com.gzhuxn.system.api;

import com.gzhuxn.common.core.exception.ServiceException;
import com.gzhuxn.common.core.exception.user.UserException;
import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import com.gzhuxn.system.api.domain.vo.RemoteUserVo;
import com.gzhuxn.system.api.model.LoginUser;
import com.gzhuxn.system.api.model.XcxLoginUser;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
public interface RemoteUserService {

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param tenantId 租户id
     * @return 结果
     */
    LoginUser getUserInfo(String username, String tenantId) throws UserException;

    /**
     * 通过用户id查询用户信息
     *
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 结果
     */
    LoginUser getUserInfo(Long userId, String tenantId) throws UserException;

    /**
     * 通过手机号查询用户信息
     *
     * @param phoneNumber 手机号
     * @param tenantId    租户id
     * @return 结果
     */
    LoginUser getUserInfoByPhoneNumber(String phoneNumber, String tenantId) throws UserException;

    /**
     * 通过邮箱查询用户信息
     *
     * @param email    邮箱
     * @param tenantId 租户id
     * @return 结果
     */
    LoginUser getUserInfoByEmail(String email, String tenantId) throws UserException;

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    XcxLoginUser getUserInfoByOpenid(String openid) throws UserException;

    /**
     * 注册用户信息
     *
     * @param remoteUserBo 用户信息
     * @return 结果
     */
    Boolean registerUserInfo(RemoteUserBo remoteUserBo) throws UserException, ServiceException;

    /**
     * 更新用户信息
     *
     * @param remoteUserBo 用户信息
     * @return 结果
     */
    Boolean updateUserInfo(RemoteUserBo remoteUserBo) throws UserException, ServiceException;

    /**
     * 更新用户头像
     *
     * @param remoteUserBo 用户信息
     * @return 结果
     */
    Boolean updateUserAvatar(RemoteUserBo remoteUserBo) throws UserException, ServiceException;

    /**
     * 通过userId查询用户账户
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectUserNameById(Long userId);

    /**
     * 通过用户ID查询用户昵称
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectNicknameById(Long userId);

    /**
     * 通过用户ID查询用户账户
     *
     * @param userIds 用户ID 多个用逗号隔开
     * @return 用户名称
     */
    String selectNicknameByIds(String userIds);

    /**
     * 通过用户ID查询用户手机号
     *
     * @param userId 用户id
     * @return 用户手机号
     */
    String selectPhoneNumberById(Long userId);

    /**
     * 通过用户ID查询用户邮箱
     *
     * @param userId 用户id
     * @return 用户邮箱
     */
    String selectEmailById(Long userId);

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param ip     IP地址
     */
    void recordLoginInfo(Long userId, String ip);

    /**
     * 通过用户ID查询用户列表
     *
     * @param userIds 用户ids
     * @return 用户列表
     */
    List<RemoteUserVo> selectListByIds(List<Long> userIds);

    /**
     * 通过用户ID查询用户列表
     *
     * @param userId 用户id
     * @return 用户列表
     */
    RemoteUserVo selectById(Long userId);

    /**
     * 通过角色ID查询用户ID
     *
     * @param roleIds 角色ids
     * @return 用户ids
     */
    List<Long> selectUserIdsByRoleIds(List<Long> roleIds);

    /**
     * 更新用户关注公众号状态
     *
     * @param unionId   unionId
     * @param mpOpenId  mpOpenId
     * @param subscribe 值
     * @param time      时间
     */
    void updateMpSubscribe(String unionId, String mpOpenId, Integer subscribe, LocalDateTime time);

    /**
     * 通过用户ID查询用户关注公众号的openId
     *
     * @param userIds 用户ids
     * @return openId列表
     */
    List<RemoteUserVo> selectMpOpenIdsByIds(List<Long> userIds);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 用户状态
     * @return 更新结果
     */
    Boolean updateUserStatus(Long userId, String status);

    /**
     * 注销用户
     *
     * @param userId 用户ID
     * @return 注销结果
     */
    Boolean logoffUser(Long userId);

    /**
     * 软删除用户（标记为删除状态）
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    Boolean softDeleteUser(Long userId);

    /**
     * 检查用户是否已注销
     *
     * @param userId 用户ID
     * @return 是否已注销
     */
    Boolean isUserLoggedOff(Long userId);
}
