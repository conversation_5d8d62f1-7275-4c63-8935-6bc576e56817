package com.gzhuxn.system.api.enums;

import lombok.Getter;

/**
 * 地区级别枚举
 * <p>
 * 级别：1省/直辖市、2市、3区/县、4街道/镇/乡、5居委会/村
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 9:07
 */
@Getter
public enum DistrictLevel {
    PROVINCE(1, "省/直辖市"),
    CITY(2, "市"),
    COUNTY(3, "区/县"),
    STREET(4, "街道/镇/乡"),
    VILLAGE(5, "居委会/村");

    private final Integer level;
    private final String desc;

    DistrictLevel(int level, String desc) {
        this.level = level;
        this.desc = desc;
    }
}
