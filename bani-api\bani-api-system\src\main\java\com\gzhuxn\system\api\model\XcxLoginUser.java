package com.gzhuxn.system.api.model;

import com.gzhuxn.common.core.enums.EnableStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 小程序登录用户身份权限
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class XcxLoginUser extends LoginUser {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 小程序openId
     */
    private String openId;

    /**
     * 公众平台ID
     */
    private String unionId;

    /**
     * 公众号openId
     */
    private String mpOpenId;

    /**
     * 是否是短期用户,1是、0否
     *
     * @see EnableStatus
     */
    private Integer shortFlag;
}
