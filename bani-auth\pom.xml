<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-auth</artifactId>

    <description>
        bani-auth 认证授权中心
    </description>

    <dependencies>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-sentinel</artifactId>
        </dependency>

        <!-- bani Common Security-->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-social</artifactId>
        </dependency>

        <!-- bani Common Log -->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-api-resource</artifactId>
        </dependency>
        <!--微信小程序SDK-->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-wx</artifactId>
        </dependency>

        <!-- 自定义负载均衡(多团队开发使用) -->
        <!--        <dependency>-->
        <!--            <groupId>com.gzhuxn</groupId>-->
        <!--            <artifactId>bani-common-loadbalancer</artifactId>-->
        <!--        </dependency>-->

        <!-- ELK 日志收集 -->
        <!--        <dependency>-->
        <!--            <groupId>com.gzhuxn</groupId>-->
        <!--            <artifactId>bani-common-logstash</artifactId>-->
        <!--        </dependency>-->

        <!-- skywalking 日志收集 -->
        <!--        <dependency>-->
        <!--            <groupId>com.gzhuxn</groupId>-->
        <!--            <artifactId>bani-common-skylog</artifactId>-->
        <!--        </dependency>-->

        <!-- prometheus 监控 -->
        <!--        <dependency>-->
        <!--            <groupId>com.gzhuxn</groupId>-->
        <!--            <artifactId>bani-common-prometheus</artifactId>-->
        <!--        </dependency>-->

    </dependencies>

    <!--    <build>-->
    <!--        <finalName>${project.artifactId}</finalName>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.springframework.boot</groupId>-->
    <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--                <version>${spring-boot.version}</version>-->
    <!--                <executions>-->
    <!--                    <execution>-->
    <!--                        <goals>-->
    <!--                            <goal>repackage</goal>-->
    <!--                        </goals>-->
    <!--                    </execution>-->
    <!--                </executions>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->

</project>
