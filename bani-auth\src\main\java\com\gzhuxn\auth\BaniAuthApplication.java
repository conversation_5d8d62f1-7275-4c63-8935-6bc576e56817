//package com.gzhuxn.auth;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
//import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
//
/// **
// * 认证授权中心
// *
// * <AUTHOR>
// */
//@Slf4j
//@EnableDubbo
//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
//public class BaniAuthApplication {
//    public static void main(String[] args) {
//        SpringApplication application = new SpringApplication(BaniAuthApplication.class);
//        application.setApplicationStartup(new BufferingApplicationStartup(2048));
//        application.run(args);
//        log.info("认证授权中心启动成功...");
//    }
//}
