package com.gzhuxn.auth.controller.app;

import cn.hutool.core.util.ObjectUtil;
import com.gzhuxn.auth.domain.vo.LoginVo;
import com.gzhuxn.auth.service.IAuthStrategy;
import com.gzhuxn.auth.service.SysLoginService;
import com.gzhuxn.common.core.constant.Constants;
import com.gzhuxn.common.core.constant.UserConstants;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.domain.model.LoginBody;
import com.gzhuxn.common.core.utils.MessageUtils;
import com.gzhuxn.common.core.utils.ServletUtils;
import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.common.core.utils.ValidatorUtils;
import com.gzhuxn.common.json.utils.JsonUtils;
import com.gzhuxn.system.api.RemoteClientService;
import com.gzhuxn.system.api.domain.vo.RemoteClientVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
public class AppTokenController {
    private final SysLoginService sysLoginService;
    @DubboReference
    private final RemoteClientService remoteClientService;

    /**
     * 登录方法
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public R<LoginVo> login(@RequestBody String body) {
        LoginBody loginBody = JsonUtils.parseObject(body, LoginBody.class);
        ValidatorUtils.validate(loginBody);
        String grantType = loginBody.getGrantType();
        String clientId = ServletUtils.getHeader(Constants.HEADER_CLIENT_ID_KEY);
        RemoteClientVo clientVo = remoteClientService.queryByClientId(clientId);

        // 查询不到 client 或 client 内不包含 grantType
        if (ObjectUtil.isNull(clientVo) || !StringUtils.contains(clientVo.getGrantType(), grantType)) {
            log.info("客户端id: {} 认证类型：{} 异常!.", clientId, grantType);
            return R.fail(MessageUtils.message("auth.grant.type.error"));
        } else if (!UserConstants.NORMAL.equals(clientVo.getStatus())) {
            return R.fail(MessageUtils.message("auth.grant.type.blocked"));
        }
        // 校验租户
        sysLoginService.checkTenant(loginBody.getTenantId());
        // 登录
        LoginVo loginVo = IAuthStrategy.login(body, clientVo, grantType);
        return R.ok(loginVo);
    }

    /**
     * 登出方法
     */
    @PostMapping("logout")
    public R<Void> logout() {
        sysLoginService.logout();
        return R.ok();
    }

}
