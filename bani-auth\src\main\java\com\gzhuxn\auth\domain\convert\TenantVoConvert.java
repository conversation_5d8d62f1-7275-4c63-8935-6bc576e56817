package com.gzhuxn.auth.domain.convert;

import com.gzhuxn.auth.domain.vo.TenantListVo;
import com.gzhuxn.system.api.domain.vo.RemoteTenantVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 租户vo转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TenantVoConvert extends BaseMapper<RemoteTenantVo, TenantListVo> {

}
