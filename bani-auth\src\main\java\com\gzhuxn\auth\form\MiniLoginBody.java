package com.gzhuxn.auth.form;

import com.gzhuxn.common.core.domain.model.LoginBody;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 三方登录对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MiniLoginBody extends LoginBody {
    /**
     * 小程序code
     */
    @NotBlank(message = "{xcx.code.not.blank}")
    private String code;

}
