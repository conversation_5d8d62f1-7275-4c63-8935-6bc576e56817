package com.gzhuxn.auth.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import com.gzhuxn.auth.domain.vo.LoginVo;
import com.gzhuxn.auth.form.MiniLoginBody;
import com.gzhuxn.auth.service.IAuthStrategy;
import com.gzhuxn.auth.service.SysLoginService;
import com.gzhuxn.common.core.utils.ValidatorUtils;
import com.gzhuxn.common.json.utils.JsonUtils;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.system.api.RemoteUserService;
import com.gzhuxn.system.api.domain.vo.RemoteClientVo;
import com.gzhuxn.system.api.model.XcxLoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 小程序认证策略
 *
 * <AUTHOR>
 */
@Slf4j
@Service("mini" + IAuthStrategy.BASE_NAME)
@RequiredArgsConstructor
public class MiniAuthStrategy implements IAuthStrategy {

    private final SysLoginService loginService;
    private final WxMaService wxMaService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    public LoginVo login(String body, RemoteClientVo client) {
        MiniLoginBody loginBody = JsonUtils.parseObject(body, MiniLoginBody.class);
        ValidatorUtils.validate(loginBody);
        // xcxCode 为 小程序调用 wx.login 授权后获取
        WxMaJscode2SessionResult result;
        try {
            result = wxMaService.jsCode2SessionInfo(loginBody.getCode());
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        XcxLoginUser loginUser = remoteUserService.getUserInfoByOpenid(result.getOpenid());
        loginUser.setClientKey(client.getClientKey());
        loginUser.setDeviceType(client.getDeviceType());

        SaLoginModel model = new SaLoginModel();
        model.setDevice(client.getDeviceType());
        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
        // 例如: 后台用户30分钟过期 app用户1天过期
        model.setTimeout(client.getTimeout());
        model.setActiveTimeout(client.getActiveTimeout());
        model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());
        // 生成token
        LoginHelper.login(loginUser, model);

        LoginVo loginVo = new LoginVo();
        loginVo.setAccessToken(StpUtil.getTokenValue());
        loginVo.setExpireIn(StpUtil.getTokenTimeout());
        loginVo.setClientId(client.getClientId());
        loginVo.setOpenid(result.getOpenid());
        loginVo.setShortFlag(loginUser.getShortFlag());
        return loginVo;
    }

}
