<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>bani-common-base</artifactId>
    <properties>
        <eventbus.version>2.5.1</eventbus.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.likavn</groupId>
            <artifactId>eventbus-spring-boot3-starter</artifactId>
            <version>${eventbus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.1-jre</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>listenablefuture</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-mybatis</artifactId>
        </dependency>
    </dependencies>
</project>
