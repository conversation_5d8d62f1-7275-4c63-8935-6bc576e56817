package com.gzhuxn.common.base.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gzhuxn.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BsEntity extends BaseEntity {

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;
}
