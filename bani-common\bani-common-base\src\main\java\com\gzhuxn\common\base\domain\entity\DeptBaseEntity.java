package com.gzhuxn.common.base.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeptBaseEntity extends BsEntity {

    /**
     * 创建部门
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createDept;
}
