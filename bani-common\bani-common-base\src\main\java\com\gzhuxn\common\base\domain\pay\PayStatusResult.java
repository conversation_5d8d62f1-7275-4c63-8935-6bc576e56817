package com.gzhuxn.common.base.domain.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 11:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayStatusResult {

    /**
     * 支付订单ID
     */
    private Long orderId;
    /**
     * 支付订单号
     */
    private String orderNo;

    /**
     * 支付状态
     *
     * @see com.gzhuxn.common.base.enums.PayStatus
     */
    private Integer status;
}
