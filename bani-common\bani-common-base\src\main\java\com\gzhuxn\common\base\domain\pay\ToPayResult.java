package com.gzhuxn.common.base.domain.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 封装发起支付返回结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 11:19
 */
@Data
public class ToPayResult {
    /**
     * 支付订单ID
     */
    private Long payOrderId;
    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 支付数据
     */
    private Object payData;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付类型
     */
    public Boolean isFree() {
        return amount.compareTo(BigDecimal.ZERO) == 0;
    }
}
