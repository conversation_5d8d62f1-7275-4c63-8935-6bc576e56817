package com.gzhuxn.common.base.enums;

import lombok.Getter;

/**
 * 支付状态
 * <p>
 * 支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 14:55
 */
@Getter
public enum PayStatus {
    /**
     * 待支付
     */
    WAIT_PAY(1, "待支付"),
    PAYING(2, "支付中"),
    FAIL(3, "支付失败"),
    CLOSE(4, "关闭"),
    SUCCESS(10, "支付成功");

    private final Integer value;
    private final String desc;

    PayStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value v
     * @return r
     */
    public static PayStatus of(Integer value) {
        for (PayStatus payStatus : PayStatus.values()) {
            if (payStatus.getValue().equals(value)) {
                return payStatus;
            }
        }
        throw new IllegalArgumentException("支付状态不存在");
    }


    /**
     * 微信支付状态
     *
     * @param state 状态
     * @return 状态
     */
    public static PayStatus ofWx(String state) {
        return switch (state) {
            case "SUCCESS" -> SUCCESS;
            case "NOTPAY" -> WAIT_PAY;
            case "USERPAYING" -> PAYING;
            case "PAYERROR" -> FAIL;
            case "CLOSED" -> CLOSE;
            default -> null;
        };
    }
}
