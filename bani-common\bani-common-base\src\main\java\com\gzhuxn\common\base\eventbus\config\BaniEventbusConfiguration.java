package com.gzhuxn.common.base.eventbus.config;

import com.baomidou.mybatisplus.core.toolkit.Sequence;
import com.github.likavn.eventbus.core.api.RequestIdGenerator;
import com.gzhuxn.common.base.eventbus.interceptor.DeliverAfterClearContextInterceptor;
import com.gzhuxn.common.base.eventbus.interceptor.DeliverBeforeAddContextInterceptor;
import com.gzhuxn.common.base.eventbus.interceptor.SendBeforeAddContextInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * eventbus配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 23:19
 */
@Configuration
public class BaniEventbusConfiguration {

    @Bean
    public RequestIdGenerator requestIdGenerator() throws UnknownHostException {
        Sequence sequence = new Sequence(InetAddress.getLocalHost());
        return () -> String.valueOf(sequence.nextId());
    }

    @Bean
    public SendBeforeAddContextInterceptor sendBeforeAddContextInterceptor() {
        return new SendBeforeAddContextInterceptor();
    }

    @Bean
    public DeliverBeforeAddContextInterceptor deliverBeforeAddContextInterceptor() {
        return new DeliverBeforeAddContextInterceptor();
    }

    @Bean
    public DeliverAfterClearContextInterceptor deliverAfterClearContextInterceptor() {
        return new DeliverAfterClearContextInterceptor();
    }

}
