package com.gzhuxn.common.base.eventbus.interceptor;

import com.github.likavn.eventbus.core.annotation.Order;
import com.github.likavn.eventbus.core.api.interceptor.DeliverAfterInterceptor;
import com.github.likavn.eventbus.core.metadata.data.Request;
import com.gzhuxn.common.tenant.helper.TenantHelper;

/**
 * 清除上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 23:23
 */
@Order(Integer.MAX_VALUE)
public class DeliverAfterClearContextInterceptor implements DeliverAfterInterceptor {
    @Override
    public void execute(Request<String> request, Throwable throwable) {
        TenantHelper.clearDynamic();
    }
}
