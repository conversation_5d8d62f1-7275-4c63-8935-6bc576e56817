package com.gzhuxn.common.base.eventbus.interceptor;

import com.github.likavn.eventbus.core.annotation.Order;
import com.github.likavn.eventbus.core.api.interceptor.DeliverBeforeInterceptor;
import com.github.likavn.eventbus.core.metadata.data.Request;
import com.gzhuxn.common.base.constant.BaseConstant;
import com.gzhuxn.common.tenant.helper.TenantHelper;

/**
 * 消息投递前添加上下文参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 23:21
 */
@Order(Integer.MIN_VALUE)
public class DeliverBeforeAddContextInterceptor implements DeliverBeforeInterceptor {
    @Override
    public void execute(Request<String> request) {
        // 添加上下文参数
        // 添加租户ID
        String tenantId = request.header(BaseConstant.TENANT_ID);
        if (tenantId != null) {
            TenantHelper.setDynamic(tenantId);
        }
    }
}
