package com.gzhuxn.common.base.eventbus.interceptor;


import com.github.likavn.eventbus.core.annotation.Order;
import com.github.likavn.eventbus.core.api.interceptor.SendBeforeInterceptor;
import com.github.likavn.eventbus.core.metadata.data.Request;
import com.gzhuxn.common.base.constant.BaseConstant;
import com.gzhuxn.common.tenant.helper.TenantHelper;

/**
 * 发送前拦截器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 23:14
 */
@Order(Integer.MIN_VALUE)
public class SendBeforeAddContextInterceptor implements SendBeforeInterceptor {
    @Override
    public void execute(Request<String> request) {
        // 添加上下文参数
        // 添加租户ID
        String tenantId = TenantHelper.getTenantId();
        if (tenantId != null) {
            request.addHeader(BaseConstant.TENANT_ID, tenantId);
        }
    }
}
