package com.gzhuxn.common.base.service;

import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;

/**
 * <PERSON>i api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/31 22:21
 */
public interface IBaniService<T> extends IService<T> {
    /**
     * 根据ID校验数据存在
     */
    T existById(Serializable id);

    /**
     * 根据ID校验数据存在
     */
    T existById(Serializable id, String message);

    /**
     * 校验数据存在
     */
    void validateExists(Serializable id);

    /**
     * 校验数据存在
     */
    void validateExists(Serializable id, String message);
}
