package com.gzhuxn.common.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzhuxn.common.base.service.IBaniService;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.exception.ServiceException;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 14:06
 */
public abstract class BaniServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IBaniService<T> {
    /**
     * 根据ID校验数据存在
     */
    @Override
    public T existById(Serializable id) {
        return existById(id, "数据不存在！");
    }

    /**
     * 根据ID校验数据存在
     */
    @Override
    public T existById(Serializable id, String message) {
        T t = getById(id);
        AssertUtils.notNull(t, message);
        return t;
    }

    /**
     * 校验数据存在
     */
    @Override
    public void validateExists(Serializable id) {
        validateExists(id, "数据不存在！");
    }

    /**
     * 校验数据存在
     */
    @Override
    public void validateExists(Serializable id, String message) {
        if (null == getById(id)) {
            throw new ServiceException(message);
        }
    }

    /**
     * 批量删除
     *
     * @param list list
     * @return true
     */
    @Override
    public boolean removeBatchByIds(Collection<?> list) {
        if (CollUtil.isEmpty(list)) {
            return true;
        }
        return super.removeBatchByIds(list);
    }

    /**
     * 批量插入
     *
     * @param entityList entityList
     * @return true
     */
    @Override
    public boolean saveBatch(Collection<T> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return true;
        }
        return super.saveBatch(entityList);
    }

    /**
     * 批量修改插入
     *
     * @param entityList 实体对象集合
     */
    @Override
    public boolean saveOrUpdateBatch(Collection<T> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return true;
        }
        return super.saveOrUpdateBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    /**
     * 根据ID 批量更新
     *
     * @param entityList 实体对象集合
     */
    @Override
    public boolean updateBatchById(Collection<T> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return true;
        }
        return super.updateBatchById(entityList, DEFAULT_BATCH_SIZE);
    }

}
