package com.gzhuxn.common.base.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileMagicNumber;
import cn.hutool.core.io.FileTypeUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 21:00
 */
@Slf4j
@UtilityClass
public class Func {

    /**
     * 判断是否是图片
     *
     * @param file 文件
     * @return 是否是图片
     */
    public boolean isImage(MultipartFile file) {
        try {
            return isImage(file.getInputStream());
        } catch (IOException e) {
            log.error("判断是否是图片失败", e);
            throw new RuntimeException("");
        }
    }

    /**
     * 判断是否是图片
     *
     * @param in 文件
     * @return 是否是图片
     */
    public boolean isImage(InputStream in) {
        String type = FileTypeUtil.getType(in);
        return FileMagicNumber.JPEG.getExtension().equals(type)
            || FileMagicNumber.PNG.getExtension().equals(type) || FileMagicNumber.GIF.getExtension().equals(type);
    }

    /**
     * 文件大小转换
     *
     * @param size 文件大小
     * @return 文件大小
     */
    public String fileSizeConvert(Long size) {
        String sizeStr = "";
        if (null != size) {
            if (size < 1024) {
                sizeStr = size + "Bit";
            } else if (size / 1024 < 1024) {
                sizeStr = size / 1024 + "KB";
            } else if (size / 1024 / 1024 < 1024) {
                sizeStr = size / 1024 / 1024 + "MB";
            } else {
                sizeStr = size / 1024 / 1024 / 1024 + "GB";
            }
        }
        return sizeStr;
    }


    /**
     * 计算集合的单差集，即只返回【集合1】中有，但是【集合2】中没有的元素，例如：
     *
     * <pre>
     *     subtractToList([1,2,3,4],[2,3,4,5]) -》 [1]
     * </pre>
     *
     * @param coll1 集合1
     * @param coll2 集合2
     * @param <T>   元素类型
     * @return 单差集
     */
    @SuppressWarnings("all")
    public static <T, R> List<R> subtractToMapper(Collection<T> coll1, Collection<T> coll2, Function<T, R> mapper) {
        return subtract(coll1, coll2, mapper).stream().map(mapper).toList();
    }

    /**
     * 计算集合的单差集，即只返回【集合1】中有，但是【集合2】中没有的元素，例如：
     *
     * <pre>
     *     subtractToList([1,2,3,4],[2,3,4,5]) -》 [1]
     * </pre>
     *
     * @param coll1 集合1
     * @param coll2 集合2
     * @param <T>   元素类型
     * @return 单差集
     */
    @SuppressWarnings("all")
    public static <T, R> List<T> subtract(Collection<T> coll1, Collection<T> coll2, Function<T, R> mapper) {
        if (CollUtil.isEmpty(coll1)) {
            return ListUtil.empty();
        }
        if (CollUtil.isEmpty(coll2)) {
            return ListUtil.list(true, coll1);
        }

        //将被交数用链表储存，防止因为频繁扩容影响性能
        final List<T> result = new LinkedList<>();
        Set<?> set2 = coll2.stream().map(mapper).filter(Objects::nonNull).collect(Collectors.toSet());

        coll1.stream().map(mapper).filter(Objects::nonNull).forEach(t -> {
            if (!set2.contains(t)) {
                result.add((T) t);
            }
        });
        return result;
    }

    /**
     * 构建异步缓存
     *
     * @param duration 缓存时长
     * @param loader   缓存加载器
     * @param <K>      key类型
     * @param <V>      value类型
     * @return 缓存
     */
    public static <K, V> LoadingCache<K, V> buildAsyncReloadingCache(Duration duration, CacheLoader<K, V> loader) {
        return CacheBuilder.newBuilder().refreshAfterWrite(duration).build(CacheLoader.asyncReloading(loader, Executors.newCachedThreadPool()));
    }

    /**
     * byte[] 数组转称base64图片字符串
     */
    public static String bytesToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

}
