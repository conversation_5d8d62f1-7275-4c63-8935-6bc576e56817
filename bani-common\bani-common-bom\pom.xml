<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gzhuxn</groupId>
    <artifactId>bani-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        bani-common-bom common依赖项
    </description>

    <properties>
        <revision>2.2.1</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 权限认证服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 字典 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-dict</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- RPC服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-seata</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 自定义负载均衡 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-loadbalancer</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- oss服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流功能 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等功能 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 邮件模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 短信模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- logstash日志推送模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-logstash</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- ES搜索引擎服务 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-sentinel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- skywalking日志收集模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-skylog</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- prometheus监控 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-prometheus</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 通用翻译功能 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据加解密模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- websocket模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 授权认证 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 配置中心 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-nacos</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息总线模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-bus</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- sse -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-sse</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--自定义基础模块-->
            <!-- 微信相关 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-wx</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 基础模块 -->
            <dependency>
                <groupId>com.gzhuxn</groupId>
                <artifactId>bani-common-base</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
