package com.gzhuxn.common.core.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * 获取i18n资源文件
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MessageUtils {

    private static final MessageSource MESSAGE_SOURCE = SpringUtils.getBean(MessageSource.class);

    /**
     * 根据消息键和参数 获取消息 委托给spring messageSource
     *
     * @param code 消息键
     * @param args 参数
     * @return 获取国际化翻译值
     */
    public static String message(String code, Object... args) {
        try {
            return MESSAGE_SOURCE.getMessage(code, args, LocaleContextHolder.getLocale());
        } catch (NoSuchMessageException e) {
            return code;
        }
    }

    /**
     * 根据消息键和参数 获取消息 委托给spring messageSource
     *
     * @param message 消息键
     * @return 获取国际化翻译值
     */
    public static String covertMessage(String message) {
        String[] segments = message.split("\\{");
        if (segments.length <= 1) {
            return message;
        }
        // 遍历
        List<String> params = new ArrayList<>(segments.length / 2);
        String segment;
        for (int i = 1; i < segments.length; i++) {
            segment = segments[i];
            if (segment.contains("}")) {
                params.add("{" + segment.substring(0, segment.indexOf("}") + 1));
            }
        }
        String code;
        String covertMsg;
        for (String param : params) {
            code = param.substring(1, params.size() - 1);
            try {
                covertMsg = MESSAGE_SOURCE.getMessage(code.trim(), null, LocaleContextHolder.getLocale());
            } catch (NoSuchMessageException e) {
                continue;
            }
            message = message.replace(param, covertMsg);
        }
        return message;
    }
}
