<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-common-mybatis</artifactId>

    <description>
        bani-common-mybatis 数据库服务
    </description>

    <dependencies>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-satoken</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-dubbo</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- sql性能分析插件 -->
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>

        <!-- Dynamic DataSource -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
            <version>${dynamic-ds.version}</version>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--        &lt;!&ndash; Oracle &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.oracle.database.jdbc</groupId>-->
        <!--            <artifactId>ojdbc8</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; 兼容oracle低版本 &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.oracle.database.nls</groupId>-->
        <!--            <artifactId>orai18n</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; PostgreSql &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.postgresql</groupId>-->
        <!--            <artifactId>postgresql</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; SqlServer &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.microsoft.sqlserver</groupId>-->
        <!--            <artifactId>mssql-jdbc</artifactId>-->
        <!--        </dependency>-->

    </dependencies>

</project>
