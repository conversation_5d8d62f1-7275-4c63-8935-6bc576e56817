<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-common-ratelimiter</artifactId>

    <description>
        bani-common-ratelimiter 限流功能
    </description>

    <dependencies>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-redis</artifactId>
        </dependency>
    </dependencies>

</project>
