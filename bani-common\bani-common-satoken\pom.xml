<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-common-satoken</artifactId>

    <description>
        bani-common-satoken 权限认证服务
    </description>

    <dependencies>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>

        <!-- Sa-Token 整合 jwt -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>${satoken.version}</version>
        </dependency>

        <!-- bani Api System -->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-api-system</artifactId>
        </dependency>

        <!-- bani Common Redis-->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

    </dependencies>

</project>
