<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-common-sms</artifactId>

    <description>
        bani-common-sms 短信模块
    </description>

    <dependencies>

        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-spring-boot-starter</artifactId>
        </dependency>

        <!-- bani Common Redis-->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-redis</artifactId>
        </dependency>

    </dependencies>
</project>
