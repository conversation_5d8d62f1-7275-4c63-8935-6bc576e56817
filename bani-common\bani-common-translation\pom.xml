<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-common-translation</artifactId>

    <description>
        bani-common-translation 通用翻译功能
    </description>

    <dependencies>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-api-resource</artifactId>
        </dependency>

    </dependencies>

</project>
