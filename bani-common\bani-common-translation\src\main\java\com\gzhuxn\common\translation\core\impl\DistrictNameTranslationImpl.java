package com.gzhuxn.common.translation.core.impl;

import com.gzhuxn.common.translation.annotation.TranslationType;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.common.translation.core.TranslationInterface;
import com.gzhuxn.system.api.RemoteDistrictService;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

/**
 * 区域翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.DISTRICT_CODE_TO_NAME)
public class DistrictNameTranslationImpl implements TranslationInterface<String> {

    @DubboReference
    private RemoteDistrictService remoteDistrictService;

    @Override
    public String translation(Object key, String other) {
        if (null == key) {
            return null;
        }
        return remoteDistrictService.selectNameByCode(key.toString());
    }
}
