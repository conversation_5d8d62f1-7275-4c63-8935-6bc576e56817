package com.gzhuxn.common.translation.core.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.gzhuxn.common.translation.annotation.TranslationType;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.common.translation.core.TranslationInterface;
import com.gzhuxn.common.translation.domain.OssFile;
import com.gzhuxn.resource.api.RemoteFileService;
import com.gzhuxn.resource.api.domain.RemoteFile;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

import java.util.Collections;
import java.util.List;

/**
 * 文件翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.TO_FILE_OSS)
public class OssFilesTranslationImpl implements TranslationInterface<Object> {

    @DubboReference(mock = "true")
    private RemoteFileService remoteFileService;

    @Override
    public Object translation(Object key, String other) {
        String fileIds = null;
        boolean single = false;
        if (key instanceof String || key instanceof Long) {
            fileIds = key.toString();
            single = (key instanceof Long);
        }
        List<RemoteFile> remoteFiles = null;
        if (null != fileIds) {
            remoteFiles = remoteFileService.selectByIds(fileIds);
            if (CollUtil.isEmpty(remoteFiles)) {
                return single ? null : Collections.emptyList();
            }
        }
        if (CollUtil.isEmpty(remoteFiles)) {
            return single ? null : Collections.emptyList();
        }
        if (single) {
            return BeanUtil.copyProperties(remoteFiles.getFirst(), OssFile.class);
        }
        return BeanUtil.copyToList(remoteFiles, OssFile.class);
    }
}
