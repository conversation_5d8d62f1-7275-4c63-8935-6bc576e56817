package com.gzhuxn.common.translation.core.impl;

import cn.hutool.core.bean.BeanUtil;
import com.gzhuxn.common.translation.annotation.TranslationType;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.common.translation.core.TranslationInterface;
import com.gzhuxn.common.translation.domain.OssViewFile;
import com.gzhuxn.resource.api.RemoteFileService;
import com.gzhuxn.resource.api.domain.RemoteFile;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

import java.util.Collections;
import java.util.List;

/**
 * 文件翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.TO_FILE_VIEW_OSS)
public class OssViewFilesTranslationImpl implements TranslationInterface<Object> {

    @DubboReference(mock = "true")
    private RemoteFileService remoteFileService;

    @Override
    public Object translation(Object key, String other) {
        String fileIds = null;
        boolean isSingle = false;
        if (key instanceof String || key instanceof Long) {
            fileIds = key.toString();
            isSingle = (key instanceof Long);
        }
        List<RemoteFile> remoteFiles = null;
        if (null != fileIds) {
            remoteFiles = remoteFileService.selectByIds(fileIds);
        }
        if (null == remoteFiles || remoteFiles.isEmpty()) {
            return isSingle ? null : Collections.emptyList();
        }
        return isSingle ? BeanUtil.copyProperties(remoteFiles.getFirst(), OssViewFile.class)
            : BeanUtil.copyToList(remoteFiles, OssViewFile.class);
    }
}
