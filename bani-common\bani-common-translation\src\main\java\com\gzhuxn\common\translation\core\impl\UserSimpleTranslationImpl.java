package com.gzhuxn.common.translation.core.impl;

import com.gzhuxn.common.translation.annotation.TranslationType;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.common.translation.core.TranslationInterface;
import com.gzhuxn.common.translation.domain.UserSimple;
import com.gzhuxn.resource.api.RemoteFileService;
import com.gzhuxn.resource.api.domain.RemoteFile;
import com.gzhuxn.system.api.RemoteUserService;
import com.gzhuxn.system.api.domain.vo.RemoteUserVo;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

import java.util.List;

/**
 * 用户名翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.USER_ID_TO_SIMPLE_USER)
public class UserSimpleTranslationImpl implements TranslationInterface<UserSimple> {

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteFileService remoteFileService;


    @Override
    public UserSimple translation(Object key, String other) {
        Long userId = (Long) key;
        RemoteUserVo remoteUserVo = remoteUserService.selectById(userId);
        UserSimple ret = new UserSimple(userId);
        if (null != remoteUserVo) {
            ret.setNickName(remoteUserVo.getNickName());
            List<RemoteFile> remoteFiles = remoteFileService.selectByIds(remoteUserVo.getAvatar().toString());
            if (!remoteFiles.isEmpty()) {
                RemoteFile first = remoteFiles.getFirst();
                ret.setUrl(first.getUrl());
                ret.setSmallUrl(first.getSmallUrl());
            }
        } else {
            ret.setLogoffFlag(Boolean.TRUE);
        }
        return ret;
    }
}
