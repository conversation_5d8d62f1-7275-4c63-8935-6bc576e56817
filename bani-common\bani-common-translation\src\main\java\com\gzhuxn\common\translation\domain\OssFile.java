package com.gzhuxn.common.translation.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 文件对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/11 23:25
 */
@Data
public class OssFile implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * oss主键
     */
    private Long ossId;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 文件地址
     */
    private String smallUrl;

    /**
     * 原名
     */
    private String originalName;

    /**
     * 文件后缀名
     */
    private String fileSuffix;
}
