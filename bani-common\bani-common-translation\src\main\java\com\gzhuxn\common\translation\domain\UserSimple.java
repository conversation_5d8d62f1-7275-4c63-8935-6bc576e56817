package com.gzhuxn.common.translation.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户简单信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 22:37
 */
@Data
public class UserSimple implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 对方用户ID
     */
    private Long userId;
    /**
     * 对方用户名称
     */
    private String nickName;
    /**
     * 文件url
     */
    private String url;
    /**
     * 缩略图url
     */
    private String smallUrl;
    /**
     * 对方用户是否已注销系统
     */
    private Boolean logoffFlag;

    public UserSimple(Long userId) {
        this.userId = userId;
        this.logoffFlag = Boolean.FALSE;
    }
}
