<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-common</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>bani-common-wx</artifactId>
    <properties>
        <wx-tools.version>4.6.0</wx-tools.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-redis</artifactId>
        </dependency>
        <!--微信小程序SDK-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>${wx-tools.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>${wx-tools.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <version>${wx-tools.version}</version>
        </dependency>
    </dependencies>
</project>
