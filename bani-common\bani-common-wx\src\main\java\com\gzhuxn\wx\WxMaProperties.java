package com.gzhuxn.wx;

import com.gzhuxn.wx.constant.WxConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 微信小程序配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 17:36
 */
@Data
@ConfigurationProperties(prefix = WxConstant.MINI_APP)
public class WxMaProperties {

    /**
     * 设置微信小程序的appid
     */
    private String appId;

    /**
     * 设置微信小程序的Secret
     */
    private String secret;

    /**
     * 设置微信小程序消息服务器配置的token
     */
    private String token;

    /**
     * 设置微信小程序消息服务器配置的EncodingAESKey
     */
    private String aesKey;

    /**
     * 消息格式，XML或者JSON
     */
    private String msgDataFormat;
}
