package com.gzhuxn.wx;

import com.gzhuxn.wx.constant.WxConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 微信公众号配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 17:13
 */
@Data
@ConfigurationProperties(prefix = WxConstant.MP)
public class WxMpProperties {
    /**
     * 设置微信公众号的appid
     */
    private String appId;

    /**
     * 设置微信公众号的app secret
     */
    private String secret;

    /**
     * 设置微信公众号的token
     */
    private String token;

    /**
     * 设置微信公众号的EncodingAESKey
     */
    private String aesKey;
}
