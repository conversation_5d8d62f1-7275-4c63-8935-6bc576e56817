package com.gzhuxn.wx;

import com.gzhuxn.wx.constant.WxConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 17:30
 */
@Data
@ConfigurationProperties(prefix = WxConstant.PAY)
public class WxPayProperties {
    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户密钥
     */
    private String mchKey;

    /**
     * apiclient_cert.p12文件的绝对路径，或者如果放在项目中，请以classpath:开头指定
     */
    private String keyPath;

    /**
     * 微信支付回调地址
     */
    private String notifyUrl;

}
