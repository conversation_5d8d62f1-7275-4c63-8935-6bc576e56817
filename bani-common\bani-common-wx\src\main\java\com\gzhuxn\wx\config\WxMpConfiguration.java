package com.gzhuxn.wx.config;

import com.gzhuxn.wx.WxMpProperties;
import com.gzhuxn.wx.constant.WxConstant;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信公众号配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 17:16
 */
@AllArgsConstructor
@Configuration
@EnableConfigurationProperties(WxMpProperties.class)
@ConditionalOnProperty(prefix = WxConstant.MP, name = "enable", havingValue = "true")
public class WxMpConfiguration {

    @Bean
    public WxMpService wxMpService(WxMpProperties properties, RedissonClient redissonClient) {
        WxMpService service = new WxMpServiceImpl();
        WxMpDefaultConfigImpl configStorage = new WxMpRedissonConfigImpl(redissonClient, WxConstant.MP);
        configStorage.setAppId(properties.getAppId());
        configStorage.setSecret(properties.getSecret());
        configStorage.setToken(properties.getToken());
        configStorage.setAesKey(properties.getAesKey());
        service.setWxMpConfigStorage(configStorage);
        return service;
    }

}
