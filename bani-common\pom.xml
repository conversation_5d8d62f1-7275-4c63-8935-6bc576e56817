<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>bani-common-bom</module>
        <module>bani-common-alibaba-bom</module>
        <module>bani-common-log</module>
        <module>bani-common-dict</module>
        <module>bani-common-excel</module>
        <module>bani-common-core</module>
        <module>bani-common-redis</module>
        <module>bani-common-doc</module>
        <module>bani-common-security</module>
        <module>bani-common-satoken</module>
        <module>bani-common-web</module>
        <module>bani-common-mybatis</module>
        <module>bani-common-job</module>
        <module>bani-common-dubbo</module>
        <module>bani-common-seata</module>
        <module>bani-common-loadbalancer</module>
        <module>bani-common-oss</module>
        <module>bani-common-ratelimiter</module>
        <module>bani-common-idempotent</module>
        <module>bani-common-mail</module>
        <module>bani-common-sms</module>
        <module>bani-common-logstash</module>
        <module>bani-common-elasticsearch</module>
        <module>bani-common-sentinel</module>
        <module>bani-common-skylog</module>
        <module>bani-common-prometheus</module>
        <module>bani-common-translation</module>
        <module>bani-common-sensitive</module>
        <module>bani-common-json</module>
        <module>bani-common-encrypt</module>
        <module>bani-common-tenant</module>
        <module>bani-common-websocket</module>
        <module>bani-common-social</module>
        <module>bani-common-nacos</module>
        <module>bani-common-bus</module>
        <module>bani-common-sse</module>
        <module>bani-common-wx</module>
        <module>bani-common-base</module>
    </modules>

    <artifactId>bani-common</artifactId>
    <packaging>pom</packaging>

    <description>
        bani-common通用模块
    </description>

</project>
