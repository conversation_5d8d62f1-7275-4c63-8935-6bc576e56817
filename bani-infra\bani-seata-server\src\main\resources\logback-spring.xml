<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 1999-2019 Seata.io Group.
  ~
  ~  Licensed under the Apache License, Version 2.0 (the "License");
  ~  you may not use this file except in compliance with the License.
  ~  You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~  Unless required by applicable law or agreed to in writing, software
  ~  distributed under the License is distributed on an "AS IS" BASIS,
  ~  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~  See the License for the specific language governing permissions and
  ~  limitations under the License.
  -->

<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- Context listeners -->
    <contextListener class="io.seata.server.logging.listener.SystemPropertyLoggerContextListener"/>
    <!-- common properties -->
    <springProperty name="PORT" source="server.port" defaultValue="7091"/>
    <springProperty name="APPLICATION_NAME" source="spring.application.name" defaultValue="seata-server"/>

    <!-- 日志存放路径 -->
    <property name="log.path" value="logs/${project.artifactId}"/>
    <!-- 日志输出格式 -->
    <property name="console.log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}) - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <include resource="logback-common.xml"/>

    <include resource="logback-logstash.xml"/>

    <!-- 开启 skywalking 日志收集 -->
    <include resource="logback-skylog.xml"/>

    <root level="INFO">
        <appender-ref ref="console"/>
    </root>
</configuration>
