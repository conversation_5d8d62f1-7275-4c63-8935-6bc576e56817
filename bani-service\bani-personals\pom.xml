<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gzhuxn</groupId>
        <artifactId>bani-service</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bani-personals</artifactId>

    <description>
        personals交友核心服务
    </description>

    <dependencies>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-wx</artifactId>
        </dependency>

        <!-- bani Api System -->
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-api-resource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-api-personals</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gzhuxn</groupId>
            <artifactId>bani-common-idempotent</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
