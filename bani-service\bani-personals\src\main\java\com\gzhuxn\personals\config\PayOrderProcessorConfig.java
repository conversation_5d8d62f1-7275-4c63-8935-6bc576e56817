package com.gzhuxn.personals.config;

import cn.hutool.core.collection.CollUtil;
import com.gzhuxn.personals.factory.pay.PayOrderProcessor;
import com.gzhuxn.personals.factory.pay.PayOrderProcessorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.List;

/**
 * 支付订单处理器配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:42
 */
@Configuration
public class PayOrderProcessorConfig {

    @Bean
    public PayOrderProcessorFactory payOrderProcessorFactory(@Lazy @Autowired(required = false) List<PayOrderProcessor<?>> processors) {
        PayOrderProcessorFactory processorFactory = new PayOrderProcessorFactory();
        if (CollUtil.isNotEmpty(processors)) {
            processors.forEach(processorFactory::register);
        }
        return processorFactory;
    }

}
