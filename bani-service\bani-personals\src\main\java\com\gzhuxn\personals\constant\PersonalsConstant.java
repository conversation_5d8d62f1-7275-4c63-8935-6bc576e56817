package com.gzhuxn.personals.constant;

/**
 * 常量
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public interface PersonalsConstant {
    /**
     * 支付超时时间，单位：秒
     * <p>
     * 默认60秒
     */
    long PAY_TIME_OUT = 60L;
    /**
     * 支付订单关单时间，单位：秒
     * <p>
     * 默认15分钟
     */
    long PAY_ORDER_CLOSE_TIME = 60L * 15;

    /**
     * 小程序消息跳转路径
     */
    String MP_MSG_JUMP_PATH_KEY = "pagePath";

    /**
     * 用户注销延时时间，单位：秒
     * <p>
     * 默认7天
     */
    long USER_LOGOFF_DELAY_TIME = 60L * 60 * 24 * 7;
}
