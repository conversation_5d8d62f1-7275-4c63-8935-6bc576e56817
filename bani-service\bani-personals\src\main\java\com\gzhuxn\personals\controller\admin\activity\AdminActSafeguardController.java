package com.gzhuxn.personals.controller.admin.activity;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.admin.activity.bo.AdminActivitySafeguardBo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardPageVo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardVo;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBo;
import com.gzhuxn.personals.service.activity.IActSafeguardService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 活动-活动保障项目
 * 前端访问路由地址为:/personals/activity/safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/act/safeguard")
public class AdminActSafeguardController extends BaseController {

    private final IActSafeguardService activitySafeguardService;

    /**
     * 查询活动-活动保障项目列表
     */
    @SaCheckPermission("personals:activity:safeguard:page")
    @GetMapping("/page")
    public TableDataInfo<AdminActivitySafeguardPageVo> page(ActSafeguardBo bo, PageQuery pageQuery) {
        return activitySafeguardService.queryAdminPageList(bo, pageQuery);
    }


    /**
     * 获取活动-活动保障项目详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:activity:safeguard:detail")
    @GetMapping("/detail")
    public R<AdminActivitySafeguardVo> getDetail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        // 获取保障项目基本信息
        AdminActivitySafeguardVo safeguardVo = activitySafeguardService.getAdminDetail(id);
        return R.ok(safeguardVo);
    }

    /**
     * 新增活动-活动保障项目
     */
    @RepeatSubmit()
    @SaCheckPermission("personals:activity:safeguard:create")
    @Log(title = "活动保障项目-新增", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody AdminActivitySafeguardBo bo) {
        return toAjax(activitySafeguardService.insertByBo(bo));
    }

    /**
     * 修改活动-活动保障项目
     */
    @SaCheckPermission("personals:activity:safeguard:update")
    @Log(title = "活动保障项目-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AdminActivitySafeguardBo bo) {
        return toAjax(activitySafeguardService.updateByBo(bo));
    }

    /**
     * 删除活动-活动保障项目
     *
     * @param bo 主键串
     */
    @SaCheckPermission("personals:activity:safeguard:delete")
    @Log(title = "活动保障项目-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> remove(@Validated @RequestBody DeleteBo bo) {
        return toAjax(activitySafeguardService.deleteWithValidByIds(bo.getIds(), false));
    }
}
