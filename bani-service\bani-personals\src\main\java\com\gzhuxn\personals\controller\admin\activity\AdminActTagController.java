package com.gzhuxn.personals.controller.admin.activity;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.admin.activity.bo.AdminActTagStatusUpBo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActTagVo;
import com.gzhuxn.personals.domain.activity.bo.ActTagBo;
import com.gzhuxn.personals.service.activity.IActTagService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 活动-话题管理
 * 前端访问路由地址为:/personals/tag
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/act/tag")
public class AdminActTagController extends BaseController {

    private final IActTagService actTagService;

    /**
     * 活动话题-话题管理列表
     */
    @SaCheckPermission("personals:act:tag:page")
    @GetMapping("/page")
    public TableDataInfo<AdminActTagVo> page(ActTagBo bo, PageQuery pageQuery) {
        return actTagService.queryAdminPageList(bo, pageQuery);
    }

    /**
     * 活动话题-话题管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:act:tag:detail")
    @GetMapping("/detail")
    public R<AdminActTagVo> detail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(actTagService.queryAdminById(id));
    }

    /**
     * 活动话题-创建
     */
    @SaCheckPermission("personals:act:tag:create")
    @Log(title = "活动话题-创建", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody ActTagBo bo) {
        return toAjax(actTagService.insertByBo(bo));
    }

    /**
     * 活动话题-修改
     */
    @SaCheckPermission("personals:act:tag:update")
    @Log(title = "活动话题-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody ActTagBo bo) {
        return toAjax(actTagService.updateByBo(bo));
    }

    /**
     * 活动话题-修改状态
     */
    @SaCheckPermission("personals:act:tag:update")
    @Log(title = "活动话题-修改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated(EditGroup.class) @RequestBody AdminActTagStatusUpBo bo) {
        return toAjax(actTagService.updateStatus(bo));
    }

    /**
     * 活动话题-删除
     */
    @SaCheckPermission("personals:act:tag:delete")
    @Log(title = "活动话题-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@Valid DeleteBo bo) {
        return toAjax(actTagService.deleteWithValidByIds(bo.getIds(), true));
    }
}
