package com.gzhuxn.personals.controller.admin.activity;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.app.activity.bo.AppActivityBo;
import com.gzhuxn.personals.domain.activity.bo.ActivityBo;
import com.gzhuxn.personals.domain.activity.vo.ActivityVo;
import com.gzhuxn.personals.service.activity.IActivityService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 活动-活动管理
 * 前端访问路由地址为:/personals/activity
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/activity")
public class AdminActivityController extends BaseController {

    private final IActivityService activityService;

    /**
     * 查询活动-活动管理列表
     */
    @SaCheckPermission("personals:activity:page")
    @GetMapping("/page")
    public TableDataInfo<ActivityVo> page(ActivityBo bo, PageQuery pageQuery) {
        return activityService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取活动-活动管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:activity:detail")
    @GetMapping("/detail")
    public R<ActivityVo> getInfo(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(activityService.queryById(id));
    }

    /**
     * 新增活动-活动管理
     */
    @RepeatSubmit()
    @SaCheckPermission("personals:activity:create")
    @Log(title = "活动管理-新增", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody AppActivityBo bo) {
        return toAjax(activityService.insertByBo(bo));
    }

    /**
     * 修改活动-活动管理
     */
    @SaCheckPermission("personals:activity:update")
    @Log(title = "活动管理-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppActivityBo bo) {
        return toAjax(activityService.updateByBo(bo));
    }

    /**
     * 删除活动-活动管理
     *
     * @param bo 主键串
     */
    @SaCheckPermission("personals:activity:delete")
    @Log(title = "活动管理-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> remove(@Validated @RequestBody DeleteBo bo) {
        return toAjax(activityService.deleteWithValidByIds(bo.getIds(), false));
    }
}
