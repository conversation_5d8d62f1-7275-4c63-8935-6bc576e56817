package com.gzhuxn.personals.controller.admin.activity.bo;

import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBo;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardItemBo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 活动-活动保障业务对象 act_safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminActivitySafeguardBo extends ActSafeguardBo {

    /**
     * 保障项目明细列表
     */
    @NotEmpty(message = "保障项目明细不能为空", groups = {AddGroup.class, EditGroup.class})
    @Valid
    private List<ActSafeguardItemBo> items;
}
