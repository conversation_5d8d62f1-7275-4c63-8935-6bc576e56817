package com.gzhuxn.personals.controller.admin.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.common.excel.annotation.ExcelDictFormat;
import com.gzhuxn.common.excel.convert.ExcelDictConvert;
import com.gzhuxn.common.translation.annotation.Translation;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.personals.domain.activity.ActTag;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 活动-话题管理视图对象 act_tag
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActTag.class, convertGenerate = false)
public class AdminActTagVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 礼物名称
     */
    @ExcelProperty(value = "礼物名称")
    private String name;

    /**
     * logo路径
     */
    @ExcelProperty(value = "logo路径ID")
    private Long icon;
    /**
     * logo路径
     */
    @Translation(mapper = "icon", type = TransConstant.OSS_ID_TO_URL)
    @ExcelProperty(value = "logo路径")
    private String iconUrl;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     */
    @ExcelProperty(value = "可用状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=不可用、1可用")
    private Integer status;


}
