package com.gzhuxn.personals.controller.admin.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.common.translation.annotation.Translation;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 活动-活动保障项视图对象 act_safeguard_item
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class AdminActivitySafeguardItemVo extends ActSafeguardItemVo {

    /**
     * 图标类型
     */
    @Translation(mapper = "icon", type = TransConstant.OSS_ID_TO_URL)
    @ExcelProperty(value = "图标类型")
    private String iconUrl;
}
