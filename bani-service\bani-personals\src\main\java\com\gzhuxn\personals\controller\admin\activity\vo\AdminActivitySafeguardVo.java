package com.gzhuxn.personals.controller.admin.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 活动-活动保障视图对象 act_safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class AdminActivitySafeguardVo extends ActSafeguardVo {

    /**
     * 保障项目明细列表
     */
    private List<AdminActivitySafeguardItemVo> items;
}
