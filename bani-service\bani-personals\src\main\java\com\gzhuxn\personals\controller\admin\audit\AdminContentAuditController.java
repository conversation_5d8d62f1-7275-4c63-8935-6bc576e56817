package com.gzhuxn.personals.controller.admin.audit;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.admin.audit.bo.AdminContentAuditPassBo;
import com.gzhuxn.personals.controller.admin.audit.bo.AdminContentAuditRejectBo;
import com.gzhuxn.personals.domain.audit.ContentAuditRecord;
import com.gzhuxn.personals.domain.audit.bo.ContentAuditBo;
import com.gzhuxn.personals.domain.audit.vo.ContentAuditDetailVo;
import com.gzhuxn.personals.domain.audit.vo.ContentAuditVo;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 内容审核-人工审核
 * 前端访问路由地址为:/personals/audit
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/audit")
public class AdminContentAuditController extends BaseController {

    private final IContentAuditRecordService contentAuditRecordService;

    /**
     * 查询内容审核列表
     */
    @SaCheckPermission("personals:audit:page")
    @GetMapping("/page")
    public TableDataInfo<ContentAuditVo> page(ContentAuditBo bo, PageQuery pageQuery) {
        return contentAuditRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取内容审核详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:audit:detail")
    @GetMapping("/detail")
    public R<ContentAuditDetailVo> detail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        ContentAuditRecord record = contentAuditRecordService.selectById(id);
        if (record == null) {
            return R.fail("未找到审核记录");
        }

        // 根据不同的审核类型获取对应的详情数据
        ContentAuditDetailVo detailVo = contentAuditRecordService.getDetailById(id, AuditType.of(record.getType()));
        return R.ok(detailVo);
    }

    /**
     * 审核通过
     */
    @SaCheckPermission("personals:audit:pass")
    @Log(title = "内容审核-通过", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/pass")
    public R<Void> pass(@Validated(EditGroup.class) @RequestBody AdminContentAuditPassBo bo) {
        contentAuditRecordService.transferUserPass(bo.getId());
        return R.ok();
    }

    /**
     * 审核拒绝
     */
    @SaCheckPermission("personals:audit:reject")
    @Log(title = "内容审核-拒绝", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/reject")
    public R<Void> reject(@Validated(EditGroup.class) @RequestBody AdminContentAuditRejectBo bo) {
        contentAuditRecordService.transferUserReject(bo.getId(), bo.getAuditDesc());
        return R.ok();
    }
}
