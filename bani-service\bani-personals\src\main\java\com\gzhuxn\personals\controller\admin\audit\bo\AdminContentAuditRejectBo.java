package com.gzhuxn.personals.controller.admin.audit.bo;

import com.gzhuxn.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 内容审核决绝业务对象
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
public class AdminContentAuditRejectBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 拒绝原因
     */
    @NotBlank(message = "拒绝原因不能为空", groups = {EditGroup.class})
    private String auditDesc;
}
