package com.gzhuxn.personals.controller.admin.gift.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.translation.annotation.Translation;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.personals.domain.gift.GiftManage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 礼物-礼物管理视图对象 gift_manage
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GiftManage.class, convertGenerate = false)
public class AdminGiftManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物logo路径
     */
    private Long icon;

    /**
     * 礼物logo路径
     */
    @Translation(mapper = "icon", type = TransConstant.OSS_ID_TO_URL)
    private String iconUrl;

    /**
     * 价格：花瓣
     */
    private Integer price;

    /**
     * 免费标识（0收费、1免费）
     */
    private Integer freeFlag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     *
     * @see EnableStatus
     */
    private Integer status;

    /**
     * 版本号
     */
    private String version;
}
