package com.gzhuxn.personals.controller.admin.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserAuthPayRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;
import com.gzhuxn.personals.service.user.IUserAuthPayRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户-实名认证支付记录
 * 前端访问路由地址为:/personals/user/auth/pay
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/auth/pay")
public class UserAuthPayRecordController extends BaseController {

    private final IUserAuthPayRecordService userAuthPayRecordService;

    /**
     * 查询用户-实名认证支付记录列表
     */
    @SaCheckPermission("personals:user:auth:pay:page")
    @GetMapping("/page")
    public TableDataInfo<UserAuthPayRecordVo> page(UserAuthPayRecordBo bo, PageQuery pageQuery) {
        return userAuthPayRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取用户-实名认证支付记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:user:auth:pay:detail")
    @GetMapping("/detail")
    public R<UserAuthPayRecordVo> getInfo(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(userAuthPayRecordService.queryById(id));
    }
}
