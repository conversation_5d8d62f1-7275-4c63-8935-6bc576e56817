package com.gzhuxn.personals.controller.admin.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.excel.utils.ExcelUtil;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;
import com.gzhuxn.personals.service.user.IUserLogoffRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户-用户注销记录
 * 前端访问路由地址为:/personals/logoffRecord
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/logoffRecord")
public class UserLogoffRecordController extends BaseController {

    private final IUserLogoffRecordService userLogoffRecordService;

    /**
     * 分页查询用户注销记录列表
     */
    @SaCheckPermission("personals:logoffRecord:list")
    @GetMapping("/page")
    public TableDataInfo<UserLogoffRecordVo> page(UserLogoffRecordBo bo, PageQuery pageQuery) {
        return userLogoffRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户-用户注销记录列表
     */
    @SaCheckPermission("personals:logoffRecord:export")
    @Log(title = "用户-用户注销记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UserLogoffRecordBo bo, HttpServletResponse response) {
        List<UserLogoffRecordVo> list = userLogoffRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户-用户注销记录", UserLogoffRecordVo.class, response);
    }

    /**
     * 获取用户-用户注销记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:logoffRecord:query")
    @GetMapping("/{id}")
    public R<UserLogoffRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        return R.ok(userLogoffRecordService.queryById(id));
    }

    /**
     * 创建用户注销记录
     */
    @SaCheckPermission("personals:logoffRecord:add")
    @Log(title = "用户注销记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody UserLogoffRecordBo bo) {
        return toAjax(userLogoffRecordService.insertByBo(bo));
    }

    /**
     * 更新用户注销记录
     */
    @SaCheckPermission("personals:logoffRecord:edit")
    @Log(title = "用户注销记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody UserLogoffRecordBo bo) {
        return toAjax(userLogoffRecordService.updateByBo(bo));
    }

    /**
     * 删除用户注销记录
     */
    @SaCheckPermission("personals:logoffRecord:remove")
    @Log(title = "用户注销记录", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody com.gzhuxn.common.base.domain.DeleteBo deleteBo) {
        return toAjax(userLogoffRecordService.deleteWithValidByIds(deleteBo.getIds(), true));
    }
}
