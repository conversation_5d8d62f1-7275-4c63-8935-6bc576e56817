package com.gzhuxn.personals.controller.app.coin;

import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.app.coin.vo.AppCoinTaskVo;
import com.gzhuxn.personals.service.coin.IAppCoinTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * App端花瓣任务
 * 前端访问路由地址为:/personals/coin/task
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/coin/task")
public class AppCoinTaskController extends BaseController {

    private final IAppCoinTaskService appCoinTaskService;

    /**
     * 查询用户的花瓣任务列表
     */
    @GetMapping("/list")
    public R<List<AppCoinTaskVo>> list() {
        Long userId = LoginHelper.getUserId();
        List<AppCoinTaskVo> taskList = appCoinTaskService.queryUserTaskList(userId);
        return R.ok(taskList);
    }

    /**
     * 查询指定类型的花瓣任务列表
     *
     * @param type 任务类型;1.签到 2.新手任务
     */
    @GetMapping("/list/{type}")
    public R<List<AppCoinTaskVo>> listByType(@PathVariable Integer type) {
        Long userId = LoginHelper.getUserId();
        List<AppCoinTaskVo> taskList = appCoinTaskService.queryUserTaskListByType(userId, type);
        return R.ok(taskList);
    }

    /**
     * 查询签到任务列表
     */
    @GetMapping("/sign")
    public R<List<AppCoinTaskVo>> signTasks() {
        Long userId = LoginHelper.getUserId();
        List<AppCoinTaskVo> taskList = appCoinTaskService.queryUserTaskListByType(userId, 1);
        return R.ok(taskList);
    }

    /**
     * 查询新手任务列表
     */
    @GetMapping("/newbie")
    public R<List<AppCoinTaskVo>> newbieTasks() {
        Long userId = LoginHelper.getUserId();
        List<AppCoinTaskVo> taskList = appCoinTaskService.queryUserTaskListByType(userId, 2);
        return R.ok(taskList);
    }
}
