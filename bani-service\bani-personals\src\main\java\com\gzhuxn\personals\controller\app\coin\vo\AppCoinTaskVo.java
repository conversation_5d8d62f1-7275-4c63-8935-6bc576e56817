package com.gzhuxn.personals.controller.app.coin.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * App端花瓣任务视图对象
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class AppCoinTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 类型;1.签到 2.新手任务
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 子类型
     */
    private Integer subType;

    /**
     * 子类型描述
     */
    private String subTypeDesc;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 花瓣数量
     */
    private Integer coin;

    /**
     * 任务状态;0-未完成、1-已完成、2-已领取
     */
    private Integer status;

    /**
     * 任务状态描述
     */
    private String statusDesc;

    /**
     * 是否可以领取
     */
    private Boolean canClaim;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sort;
}
