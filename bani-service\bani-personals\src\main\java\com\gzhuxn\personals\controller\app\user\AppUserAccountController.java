package com.gzhuxn.personals.controller.app.user;

import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserAccountBo;
import com.gzhuxn.personals.domain.user.vo.UserAccountVo;
import com.gzhuxn.personals.service.user.IUserAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户-账户信息
 * 前端访问路由地址为:/personals/account
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/account")
public class AppUserAccountController extends BaseController {

    private final IUserAccountService userAccountService;

    /**
     * 查询用户-账户信息列表
     */
    @GetMapping("/page")
    public TableDataInfo<UserAccountVo> list(UserAccountBo bo, PageQuery pageQuery) {
        return userAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取用户-账户信息详细信息
     */
    @GetMapping("/detail")
    public R<UserAccountVo> detail() {
        return R.ok(userAccountService.queryById(LoginHelper.getUserId()));
    }
}
