package com.gzhuxn.personals.controller.app.user;

import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.base.domain.pay.ToPayResult;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.app.user.bo.authapply.AuthIdentityPayBo;
import com.gzhuxn.personals.controller.app.user.vo.AppAuthPayStatusVo;
import com.gzhuxn.personals.domain.user.bo.UserAuthPayRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.service.user.IUserAuthPayRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户-实名认证支付记录
 * 前端访问路由地址为:/personals/user/auth/pay
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/auth/pay")
public class AppUserAuthPayRecordController extends BaseController {

    private final IUserAuthPayRecordService userAuthPayRecordService;

    /**
     * 查询用户-实名认证支付记录列表
     */
    @GetMapping("/page")
    public TableDataInfo<UserAuthPayRecordVo> page(UserAuthPayRecordBo bo, PageQuery pageQuery) {
        bo.setUserId(LoginHelper.getUserId());
        return userAuthPayRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取用户-实名认证支付记录详细信息
     *
     * @param id 主键
     */
    @GetMapping("/detail")
    public R<UserAuthPayRecordVo> getInfo(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(userAuthPayRecordService.queryById(id));
    }

    /**
     * 实名认证支付
     */
    @Log(title = "实名认证支付-创建", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<ToPayResult> authIdentityPay(@Validated(AddGroup.class) @RequestBody AuthIdentityPayBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        return R.ok(userAuthPayRecordService.authIdentityPay(bo));
    }

    /**
     * 删除用户-实名认证支付记录
     */
    @Log(title = "实名认证支付记录-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> remove(@Validated @RequestBody DeleteBo bo) {
        return toAjax(userAuthPayRecordService.deleteWithValidByIds(bo.getIds(), true));
    }

    /**
     * 查询指定认证类型是否需要支付
     *
     * @param authType 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     */
    @GetMapping("/needPay")
    public R<AppAuthPayStatusVo> checkNeedPay(@NotNull(message = "认证类型不能为空") @RequestParam Integer authType) {
        Long userId = LoginHelper.getUserId();
        boolean needPay = userAuthPayRecordService.needPayForAuth(userId, authType);

        // 获取认证类型描述
        String authTypeDesc = getAuthTypeDesc(authType);

        AppAuthPayStatusVo result = new AppAuthPayStatusVo(authType, needPay, authTypeDesc);
        return R.ok(result);
    }

    /**
     * 获取认证类型描述
     *
     * @param authType 认证类型
     * @return 描述
     */
    private String getAuthTypeDesc(Integer authType) {
        try {
            AuditType auditType = AuditType.of(authType);
            return auditType.getDesc();
        } catch (Exception e) {
            return "未知认证类型";
        }
    }
}
