package com.gzhuxn.personals.controller.app.user;

import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;
import com.gzhuxn.personals.service.user.IUserLogoffRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户注销记录控制器
 * 前端访问路由地址为:/personals/user/logoffRecord
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/logoffRecord")
public class AppUserLogoffRecordController extends BaseController {

    private final IUserLogoffRecordService userLogoffRecordService;

    /**
     * 申请用户注销
     */
    @Log(title = "申请用户注销", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/apply")
    public R<Void> applyLogoff() {
        return toAjax(userLogoffRecordService.applyUserLogoff());
    }

    /**
     * 取消用户注销
     *
     * @param id 注销记录ID
     */
    @Log(title = "取消用户注销", businessType = BusinessType.DELETE)
    @PostMapping("/cancel/{id}")
    public R<Void> cancelLogoff(@NotNull(message = "注销记录ID不能为空") @PathVariable Long id) {
        return toAjax(userLogoffRecordService.cancelUserLogoff(id));
    }

    /**
     * 查询当前用户注销记录
     */
    @GetMapping("/current")
    public R<UserLogoffRecordVo> getCurrentLogoffRecord() {
        // 查询当前用户的注销记录
        UserLogoffRecordBo bo = new UserLogoffRecordBo();
        bo.setUserId(com.gzhuxn.common.satoken.utils.LoginHelper.getUserId());
        List<UserLogoffRecordVo> list = userLogoffRecordService.queryList(bo);

        // 返回最新的一条记录
        if (!list.isEmpty()) {
            return R.ok(list.get(0));
        }
        return R.ok();
    }
}
