package com.gzhuxn.personals.controller.app.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserSignInBo;
import com.gzhuxn.personals.domain.user.vo.UserSignInVo;
import com.gzhuxn.personals.service.user.IUserSignInService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户-签到
 * 前端访问路由地址为:/personals/signIn
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/signIn")
public class AppUserSignInController extends BaseController {

    private final IUserSignInService userSignInService;

    /**
     * 查询用户-签到列表
     */
    @SaCheckPermission("personals:signIn:list")
    @GetMapping("/list")
    public TableDataInfo<UserSignInVo> list(UserSignInBo bo, PageQuery pageQuery) {
        return userSignInService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增用户-签到
     */
    @SaCheckPermission("personals:signIn:create")
    @Log(title = "用户-签到", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody UserSignInBo bo) {
        return toAjax(userSignInService.insertByBo(bo));
    }
}
