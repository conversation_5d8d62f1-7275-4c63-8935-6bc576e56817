package com.gzhuxn.personals.controller.app.user;

import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawCreateBo;
import com.gzhuxn.personals.domain.user.vo.UserWithdrawVo;
import com.gzhuxn.personals.service.user.IUserWithdrawService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户-提现申请
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/withdraw")
public class AppUserWithdrawController extends BaseController {

    private final IUserWithdrawService userWithdrawService;

    /**
     * 查询用户-提现申请列表
     */
    @GetMapping("/page")
    public TableDataInfo<UserWithdrawVo> page(UserWithdrawBo bo, PageQuery pageQuery) {
        return userWithdrawService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取用户-提现申请详细信息
     *
     * @param id 主键
     */
    @GetMapping("/detail/{id}")
    public R<UserWithdrawVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(userWithdrawService.queryById(id));
    }

    /**
     * 发起提现申请
     */
    @Log(title = "发起提现申请", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody UserWithdrawCreateBo bo) {
        return toAjax(userWithdrawService.createWithdrawApply(bo));
    }

    /**
     * 修改用户-提现申请
     */
    @Log(title = "提现申请-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UserWithdrawBo bo) {
        return toAjax(userWithdrawService.updateByBo(bo));
    }

    /**
     * 删除用户-提现申请
     */
    @Log(title = "提现申请-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> remove(@Validated @RequestBody DeleteBo bo) {
        return toAjax(userWithdrawService.deleteWithValidByIds(bo.getIds(), true));
    }
}
