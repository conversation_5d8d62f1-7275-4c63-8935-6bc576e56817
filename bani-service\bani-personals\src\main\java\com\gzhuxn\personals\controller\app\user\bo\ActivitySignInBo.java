package com.gzhuxn.personals.controller.app.user.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 活动签到业务对象
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class ActivitySignInBo {

    /**
     * 活动记录ID
     */
    @NotNull(message = "活动记录ID不能为空")
    private Long activityRecordId;

    /**
     * 签到地址
     */
    @NotBlank(message = "签到地址不能为空")
    private String signInAddr;

    /**
     * 签到经度
     */
    @NotNull(message = "签到经度不能为空")
    private Double signInLon;

    /**
     * 签到纬度
     */
    @NotNull(message = "签到纬度不能为空")
    private Double signInLat;
}
