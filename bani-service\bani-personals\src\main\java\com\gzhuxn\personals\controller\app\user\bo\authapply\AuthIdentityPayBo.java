package com.gzhuxn.personals.controller.app.user.bo.authapply;

import com.gzhuxn.common.core.validate.AddGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 实名认证支付实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-14
 */
@Data
public class AuthIdentityPayBo {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 认证申请ID
     */
    @NotNull(message = "认证申请ID不能为空", groups = AddGroup.class)
    private Long authApplyId;

    /**
     * 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     */
    @NotNull(message = "认证类型不能为空", groups = AddGroup.class)
    private Integer authType;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = AddGroup.class)
    private BigDecimal amount;

    /**
     * 兑换花瓣数量
     */
    private Integer coin;
}
