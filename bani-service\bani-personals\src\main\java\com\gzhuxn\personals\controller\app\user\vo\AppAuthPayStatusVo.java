package com.gzhuxn.personals.controller.app.user.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 认证支付状态视图对象
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class AppAuthPayStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     */
    private Integer authType;

    /**
     * 是否需要支付;true-需要支付，false-不需要支付
     */
    private Boolean needPay;

    /**
     * 认证类型描述
     */
    private String authTypeDesc;

    public AppAuthPayStatusVo() {
    }

    public AppAuthPayStatusVo(Integer authType, Boolean needPay, String authTypeDesc) {
        this.authType = authType;
        this.needPay = needPay;
        this.authTypeDesc = authTypeDesc;
    }
}
