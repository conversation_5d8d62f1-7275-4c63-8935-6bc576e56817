package com.gzhuxn.personals.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户支付关联基础信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/28 10:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserPayBaseEntity extends UserBaseEntity {
    /**
     * 支付订单ID
     */
    private Long orderId;
    /**
     * 原费用
     */
    private BigDecimal originalAmount;

    /**
     * 实际支付的费用
     */
    private BigDecimal amount;

    /**
     * 兑换花瓣数量：平台赠送
     */
    private Integer coin;

    /**
     * 支付成功时间
     */
    private LocalDateTime payTime;

    /**
     * 支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功
     *
     * @see com.gzhuxn.common.base.enums.PayStatus
     */
    private Integer payStatus;
}
