package com.gzhuxn.personals.domain.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.base.domain.entity.BsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 活动-活动保障对象 act_safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("act_safeguard")
public class ActSafeguard extends BsEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 保障名称
     */
    private String name;

    /**
     * 费用
     */
    private BigDecimal amount;

    /**
     * 保险key
     */
    private String safeKey;

    /**
     * 状态：0未启用、1启用
     */
    private Integer status;
}
