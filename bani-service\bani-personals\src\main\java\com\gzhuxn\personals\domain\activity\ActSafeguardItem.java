package com.gzhuxn.personals.domain.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.base.domain.entity.BsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动-活动保障项对象 act_safeguard_item
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("act_safeguard_item")
public class ActSafeguardItem extends BsEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 保障项目ID
     */
    private Long safeguardId;

    /**
     * 图标类型
     */
    private Long icon;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 保障内容
     */
    private String des;
}
