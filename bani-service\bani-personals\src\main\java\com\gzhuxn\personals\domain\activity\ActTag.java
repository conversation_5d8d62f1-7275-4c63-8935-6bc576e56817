package com.gzhuxn.personals.domain.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.base.domain.entity.BsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 活动-话题管理对象 act_tag
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("act_tag")
public class ActTag extends BsEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * logo路径
     */
    private Long icon;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     *
     * @see com.gzhuxn.common.core.enums.EnableStatus
     */
    private Integer status;

}
