package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.activity.ActSafeguard;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 活动-活动保障业务对象 act_safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ActSafeguard.class, reverseConvertGenerate = false)
public class ActSafeguardBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 保障名称
     */
    @NotBlank(message = "保障名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 费用
     */
    @NotNull(message = "费用不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal amount;

    /**
     * 保险key
     */
    @NotBlank(message = "保险key不能为空", groups = {AddGroup.class, EditGroup.class})
    private String safeKey;

    /**
     * 状态：0未启用、1启用
     */
    @NotNull(message = "状态：0未启用、1启用不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;
}
