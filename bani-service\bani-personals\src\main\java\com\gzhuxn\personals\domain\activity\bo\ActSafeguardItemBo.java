package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动-活动保障项业务对象 act_safeguard_item
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ActSafeguardItem.class, reverseConvertGenerate = false)
public class ActSafeguardItemBo extends BsBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 保障项目ID
     */
    private Long safeguardId;

    /**
     * 图标类型
     */
    @NotNull(message = "图标类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long icon;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 保障内容
     */
    @NotBlank(message = "保障内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String des;


}
