package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.activity.ActTag;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动-话题管理业务对象 act_tag
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ActTag.class, reverseConvertGenerate = false)
public class ActTagBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 礼物名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * logo路径
     */
    @NotNull(message = "logo路径不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long icon;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     */
    @NotNull(message = "可用状态（0不可用、1可用）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;


}
