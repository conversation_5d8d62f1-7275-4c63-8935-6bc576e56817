package com.gzhuxn.personals.domain.activity.vo;

import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 活动-活动保障项视图对象 act_safeguard_item
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@AutoMapper(target = ActSafeguardItem.class, convertGenerate = false)
public class ActSafeguardItemStorageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 图标类型
     */
    private String icon;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 保障内容
     */
    private String des;
}
