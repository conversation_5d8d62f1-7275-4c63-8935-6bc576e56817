package com.gzhuxn.personals.domain.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.common.translation.annotation.Translation;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 活动-活动保障项视图对象 act_safeguard_item
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActSafeguardItem.class, convertGenerate = false)
public class ActSafeguardItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 保障项目ID
     */
    @ExcelProperty(value = "保障项目ID")
    private Long safeguardId;

    /**
     * 图标类型
     */
    @ExcelProperty(value = "图标类型")
    private Long icon;

    /**
     * 图标类型
     */
    @Translation(mapper = "icon", type = TransConstant.OSS_ID_TO_URL)
    @ExcelProperty(value = "图标类型")
    private String iconUrl;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String name;

    /**
     * 保障内容
     */
    @ExcelProperty(value = "保障内容")
    private String des;


}
