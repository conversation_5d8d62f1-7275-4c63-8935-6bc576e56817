package com.gzhuxn.personals.domain.activity.vo;

import com.gzhuxn.personals.domain.activity.ActSafeguard;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 活动-活动保障视图对象 act_safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@AutoMapper(target = ActSafeguard.class, convertGenerate = false)
public class ActSafeguardStorageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 保障名称
     */
    private String name;

    /**
     * 费用
     */
    private BigDecimal amount;

    /**
     * 保险key
     */
    private String key;

    /**
     * 保障项
     */
    private List<ActSafeguardItemStorageVo> items;
}
