package com.gzhuxn.personals.domain.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.activity.ActSafeguard;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 活动-活动保障视图对象 act_safeguard
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActSafeguard.class, convertGenerate = false)
public class ActSafeguardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 保障名称
     */
    @ExcelProperty(value = "保障名称")
    private String name;

    /**
     * 费用
     */
    @ExcelProperty(value = "费用")
    private BigDecimal amount;

    /**
     * 保险key
     */
    @ExcelProperty(value = "保险key")
    private String safeKey;

    /**
     * 状态：0未启用、1启用
     */
    @ExcelProperty(value = "状态：0未启用、1启用")
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
