package com.gzhuxn.personals.domain.activity.vo;

import com.gzhuxn.personals.domain.activity.ActTag;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 活动-话题管理视图对象 act_tag
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Data
@AutoMapper(target = ActTag.class, convertGenerate = false)
public class ActTagVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * logo路径
     */
    private Long icon;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     */
    private Integer status;


}
