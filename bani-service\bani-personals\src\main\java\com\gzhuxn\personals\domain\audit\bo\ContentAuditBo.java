package com.gzhuxn.personals.domain.audit.bo;

import com.gzhuxn.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 内容审核业务对象
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
public class ContentAuditBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 审核类型
     */
    private Integer type;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审核描述
     */
    private String auditDesc;

    /**
     * 业务ID
     */
    private Long businessId;
}
