package com.gzhuxn.personals.domain.audit.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 内容审核详情视图对象
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
public class ContentAuditDetailVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 业务类型
     */
    private Integer type;

    /**
     * 业务类型名称
     */
    private String typeName;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审核状态名称
     */
    private String auditStatusName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    /**
     * 审核描述
     */
    private String auditDesc;

    /**
     * 审核人
     */
    private Long auditUserId;

    /**
     * 审核人名称
     */
    private String auditUserName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 业务详情数据
     */
    private Map<String, Object> businessData;
}
