package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户提现审核业务对象
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class UserWithdrawAuditBo {

    /**
     * 提现申请ID
     */
    @NotNull(message = "提现申请ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 审核状态;1发起申请、2审核通过、3打回补充、4拒绝
     */
    @NotNull(message = "审核状态不能为空", groups = {EditGroup.class})
    private Integer auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;
}
