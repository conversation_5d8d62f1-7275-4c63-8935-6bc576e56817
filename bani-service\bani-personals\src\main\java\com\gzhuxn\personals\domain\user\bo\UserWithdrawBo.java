package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.user.UserWithdraw;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户-提现申请业务对象 user_withdraw
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserWithdraw.class, reverseConvertGenerate = false)
public class UserWithdrawBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 提现用户ID
     */
    private Long userId;

    /**
     * 提现金额
     */
    private BigDecimal amount;

    /**
     * 收款人收款二维码图片ID
     */
    private Long withdrawQrCodeImage;

    /**
     * 审核状态;1发起申请、2审核通过、3打回补充、4拒绝
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人
     */
    private Long auditUserId;

    /**
     * 汇款状态;1待汇款、2已汇款
     */
    private Integer remitStatus;

    /**
     * 汇款时间
     */
    private LocalDateTime remitTime;
}
