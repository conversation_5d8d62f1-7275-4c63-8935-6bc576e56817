package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.common.core.validate.AddGroup;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户提现申请创建业务对象
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class UserWithdrawCreateBo {

    /**
     * 提现金额（元）
     */
    @NotNull(message = "提现金额不能为空", groups = {AddGroup.class})
    @DecimalMin(value = "50.0", message = "提现金额不能少于50元", groups = {AddGroup.class})
    private BigDecimal amount;

    /**
     * 收款人收款二维码图片ID
     */
    @NotNull(message = "收款二维码不能为空", groups = {AddGroup.class})
    private Long withdrawQrCodeImage;
}
