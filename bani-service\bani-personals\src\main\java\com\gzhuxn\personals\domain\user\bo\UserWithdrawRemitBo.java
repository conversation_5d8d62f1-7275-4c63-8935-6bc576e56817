package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户提现汇款业务对象
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class UserWithdrawRemitBo {

    /**
     * 提现申请ID
     */
    @NotNull(message = "提现申请ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 汇款状态;1待汇款、2已汇款
     */
    @NotNull(message = "汇款状态不能为空", groups = {EditGroup.class})
    private Integer remitStatus;

    /**
     * 汇款备注
     */
    private String remitRemark;
}
