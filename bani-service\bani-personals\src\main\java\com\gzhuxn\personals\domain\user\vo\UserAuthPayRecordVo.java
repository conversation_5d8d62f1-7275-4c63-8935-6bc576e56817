package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserAuthPayRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户-实名认证支付记录视图对象 user_auth_pay_record
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserAuthPayRecord.class, convertGenerate = false)
public class UserAuthPayRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 认证申请ID
     */
    @ExcelProperty(value = "认证申请ID")
    private Long authApplyId;

    /**
     * 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     */
    @ExcelProperty(value = "认证类型")
    private Integer authType;

    /**
     * 原费用
     */
    @ExcelProperty(value = "原费用")
    private BigDecimal originalAmount;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private BigDecimal amount;

    /**
     * 兑换花瓣数量
     */
    @ExcelProperty(value = "兑换花瓣数量")
    private Integer coin;

    /**
     * 支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功
     */
    @ExcelProperty(value = "支付状态")
    private Integer payStatus;

    /**
     * 支付订单ID
     */
    @ExcelProperty(value = "支付订单ID")
    private Long orderId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 使用状态;0未使用、1已使用
     */
    @ExcelProperty(value = "使用状态")
    private Integer useStatus;
}
