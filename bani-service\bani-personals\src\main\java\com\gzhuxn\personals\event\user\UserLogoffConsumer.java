package com.gzhuxn.personals.event.user;

import cn.dev33.satoken.stp.StpUtil;
import com.github.likavn.eventbus.core.annotation.EventbusListener;
import com.github.likavn.eventbus.core.api.MsgDelayListener;
import com.github.likavn.eventbus.core.metadata.data.Message;
import com.gzhuxn.personals.service.user.IUserLogoffRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户注销事件消费者
 * <p>
 * 处理用户注销延时任务
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@Component
@EventbusListener
public class UserLogoffConsumer implements MsgDelayListener<UserLogoffProducer.UserLogoffEvent> {

    @Lazy
    @Resource
    private IUserLogoffRecordService userLogoffRecordService;

    /**
     * 处理用户注销延时事件
     *
     * @param msg 消息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(Message<UserLogoffProducer.UserLogoffEvent> msg) {
        UserLogoffProducer.UserLogoffEvent event = msg.getBody();
        log.info("处理用户注销延时事件，注销记录ID={}，用户ID={}", event.getLogoffRecordId(), event.getUserId());

        try {
            // 执行用户注销逻辑
            userLogoffRecordService.processUserLogoff(event.getLogoffRecordId(), event.getUserId());

            // 强制注销用户所有会话
            StpUtil.kickout(event.getUserId());

            log.info("用户注销处理完成，用户ID={}", event.getUserId());
        } catch (Exception e) {
            log.error("用户注销处理失败，注销记录ID={}，用户ID={}，错误信息：{}",
                event.getLogoffRecordId(), event.getUserId(), e.getMessage(), e);
            throw e;
        }
    }
}
