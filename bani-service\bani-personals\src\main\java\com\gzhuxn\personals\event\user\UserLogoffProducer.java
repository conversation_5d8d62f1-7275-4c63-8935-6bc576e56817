package com.gzhuxn.personals.event.user;

import com.github.likavn.eventbus.core.api.MsgSender;
import com.github.likavn.eventbus.core.metadata.data.MsgBody;
import com.gzhuxn.personals.constant.PersonalsConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 用户注销事件生产者
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Component
@RequiredArgsConstructor
public class UserLogoffProducer {
    private final MsgSender msgSender;

    /**
     * 发送用户注销延时事件
     *
     * @param logoffRecordId 注销记录ID
     * @param userId         用户ID
     */
    public void sendLogoffEvent(Long logoffRecordId, Long userId) {
        UserLogoffEvent event = new UserLogoffEvent(logoffRecordId, userId);
        msgSender.sendDelayMessage(event, PersonalsConstant.USER_LOGOFF_DELAY_TIME);
    }

    /**
     * 用户注销事件
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserLogoffEvent implements MsgBody {
        /**
         * 注销记录ID
         */
        private Long logoffRecordId;

        /**
         * 用户ID
         */
        private Long userId;

        @Override
        public String code() {
            return "user_logoff_event";
        }
    }
}
