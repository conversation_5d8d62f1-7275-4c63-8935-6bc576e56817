package com.gzhuxn.personals.factory.audit;


import com.gzhuxn.personals.enums.audit.AuditType;

/**
 * 内容审核处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:33
 */
public interface ContentAuditProcessor {

    /**
     * 获取内容审核类型
     *
     * @return 内容审核类型
     */
    default AuditType getAuditType() {
        return null;
    }

    /**
     * 获取内容审核类型
     *
     * @return 内容审核类型
     */
    default AuditType[] getAuditTypes() {
        return new AuditType[0];
    }


    /**
     * 执行处理内容审核
     *
     * @param businessId 业务ID
     */
    ContentAuditRes execute(Long businessId);

    /**
     * 通过内容审核
     *
     * @param businessId 业务ID
     */
    void pass(Long businessId);

    /**
     * 拒绝内容审核
     *
     * @param businessId 业务ID
     * @param reason     拒绝原因
     */
    void reject(Long businessId, String reason);

    /**
     * 转人工处理内容审核
     *
     * @param businessId 业务ID
     */
    default void transferUser(Long businessId) {
    }
}
