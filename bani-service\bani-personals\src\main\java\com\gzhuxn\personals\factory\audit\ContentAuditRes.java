package com.gzhuxn.personals.factory.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 审核结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/17 8:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentAuditRes {
    /**
     * 审核是否通过
     */
    private boolean pass;

    /**
     * 是否需要人工审核
     */
    private boolean transferUser = false;

    /**
     * 审核结果描述
     */
    private String message;

    /**
     * 消息参数
     */
    private Map<String, Object> params;

    /**
     * 审核通过
     */
    public static ContentAuditRes isOk() {
        return ContentAuditRes.builder().pass(true).build();
    }

    /**
     * 需要人工审核
     */
    public static ContentAuditRes transferUser() {
        return ContentAuditRes.builder().transferUser(true).build();
    }

    /**
     * 审核拒绝
     */
    public static ContentAuditRes reject(String message) {
        return ContentAuditRes.builder().pass(false).message(message).build();
    }
}
