package com.gzhuxn.personals.factory.pay;

import com.gzhuxn.personals.domain.UserPayBaseEntity;
import com.gzhuxn.personals.enums.order.OrderType;

import java.util.Optional;

/**
 * 内容审核处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:33
 */
public interface PayOrderProcessor<T extends UserPayBaseEntity> {
    /**
     * 获取订单类型
     */
    OrderType getOrderType();

    /**
     * 查询支付结果
     *
     * @param orderId 订单ID
     * @return 支付结果
     */
    Optional<T> queryByOrderId(Long orderId);

    /**
     * 支付成功
     *
     * @param entity 支付更新数据
     */
    void paySuccess(UserPayBaseEntity entity);

    /**
     * 关闭订单
     *
     * @param subEntity 关闭订单数据
     */
    void close(UserPayBaseEntity subEntity);

    /**
     * 删除订单
     *
     * @param businessId 关联订单ID
     */
    void delete(Long businessId);
}
