package com.gzhuxn.personals.factory.pay;

import com.github.likavn.eventbus.core.utils.Assert;
import com.gzhuxn.personals.domain.UserPayBaseEntity;
import com.gzhuxn.personals.enums.order.OrderType;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 支付订单处理器工厂
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:34
 */
public class PayOrderProcessorFactory {

    //处理器
    private final Map<OrderType, PayOrderProcessor<? extends UserPayBaseEntity>> processorMap = HashMap.newHashMap(4);

    /**
     * 注册处理器
     *
     * @param processor 处理器
     */
    public void register(PayOrderProcessor<? extends UserPayBaseEntity> processor) {
        Assert.isTrue(processor != null, "处理器不能为空！");

        Assert.isTrue(!processorMap.containsKey(processor.getOrderType()), "处理器已存在！");
        processorMap.put(processor.getOrderType(), processor);
    }

    /**
     * 获取处理器
     *
     * @param orderType 类型
     * @return 处理器
     */
    public Optional<PayOrderProcessor<? extends UserPayBaseEntity>> getProcessor(Integer orderType) {
        return getProcessor(OrderType.of(orderType));
    }

    /**
     * 获取处理器
     *
     * @param orderType 类型
     * @return 处理器
     */
    public Optional<PayOrderProcessor<? extends UserPayBaseEntity>> getProcessor(OrderType orderType) {
        return Optional.ofNullable(processorMap.get(orderType));
    }

}
