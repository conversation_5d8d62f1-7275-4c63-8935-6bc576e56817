package com.gzhuxn.personals.mapper.activity;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;

import java.util.List;

/**
 * 活动-活动保障项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface ActSafeguardItemMapper extends BaseMapperPlus<ActSafeguardItem, ActSafeguardItemVo> {

    /**
     * 根据活动保障ID查询活动保障项列表
     *
     * @param safeguardId 活动保障ID
     * @return 活动保障项列表
     */
    default List<ActSafeguardItem> listBySafeguardId(Long safeguardId) {
        return selectList(Wrappers.<ActSafeguardItem>lambdaQuery().eq(ActSafeguardItem::getSafeguardId, safeguardId));
    }
}
