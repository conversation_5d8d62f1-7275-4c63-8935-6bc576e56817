package com.gzhuxn.personals.mapper.activity;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardPageVo;
import com.gzhuxn.personals.domain.activity.ActSafeguard;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardVo;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * 活动-活动保障Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface ActSafeguardMapper extends BaseMapperPlus<ActSafeguard, ActSafeguardVo> {
    default LambdaQueryWrapper<ActSafeguard> buildQueryWrapper(ActSafeguardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ActSafeguard> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), ActSafeguard::getName, bo.getName());
        lqw.eq(bo.getAmount() != null, ActSafeguard::getAmount, bo.getAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getSafeKey()), ActSafeguard::getSafeKey, bo.getSafeKey());
        lqw.eq(bo.getStatus() != null, ActSafeguard::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 根据主键ID和用户ID查询
     *
     * @param ids    主键ID
     * @param userId 用户ID
     */
    default long countByIdsAndUserId(Collection<Long> ids, Long userId) {
        return selectCount(Wrappers.lambdaQuery(ActSafeguard.class)
            .in(ActSafeguard::getId, ids)
            .eq(ActSafeguard::getCreateBy, userId));
    }

    /**
     * 根据主键ID和用户ID查询
     **/
    Page<AdminActivitySafeguardPageVo> queryAdminPageList(Page<Object> build, ActSafeguardBo bo);
}
