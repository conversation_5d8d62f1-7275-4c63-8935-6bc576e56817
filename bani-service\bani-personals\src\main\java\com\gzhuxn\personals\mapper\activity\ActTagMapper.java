package com.gzhuxn.personals.mapper.activity;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.activity.ActTag;
import com.gzhuxn.personals.domain.activity.bo.ActTagBo;
import com.gzhuxn.personals.domain.activity.vo.ActTagVo;
import org.apache.commons.lang3.StringUtils;

/**
 * 活动-话题管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface ActTagMapper extends BaseMapperPlus<ActTag, ActTagVo> {

    /**
     * 查询活动-话题管理列表
     */
    default LambdaQueryWrapper<ActTag> buildQueryWrapper(ActTagBo bo) {
        LambdaQueryWrapper<ActTag> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), ActTag::getName, bo.getName());
        lqw.eq(bo.getIcon() != null, ActTag::getIcon, bo.getIcon());
        lqw.eq(bo.getSort() != null, ActTag::getSort, bo.getSort());
        lqw.eq(bo.getStatus() != null, ActTag::getStatus, bo.getStatus());
        lqw.orderByDesc(ActTag::getCreateTime);
        return lqw;
    }
}
