package com.gzhuxn.personals.mapper.coin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.coin.CoinManage;
import com.gzhuxn.personals.domain.coin.bo.CoinManageBo;
import com.gzhuxn.personals.domain.coin.vo.CoinManageVo;
import org.apache.commons.lang3.StringUtils;

/**
 * 花瓣-花瓣赠送管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface CoinManageMapper extends BaseMapperPlus<CoinManage, CoinManageVo> {


    /**
     * 构建查询条件
     */
    default LambdaQueryWrapper<CoinManage> buildQueryWrapper(CoinManageBo bo) {
        LambdaQueryWrapper<CoinManage> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getType() != null, CoinManage::getType, bo.getType());
        lqw.eq(bo.getSubType() != null, CoinManage::getSubType, bo.getSubType());
        lqw.like(StringUtils.isNotBlank(bo.getName()), CoinManage::getName, bo.getName());
        lqw.eq(bo.getCoin() != null, CoinManage::getCoin, bo.getCoin());
        return lqw;
    }
}
