package com.gzhuxn.personals.mapper.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.domain.user.bo.UserAuthApplyBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthApplyVo;

import java.util.List;
import java.util.Map;

/**
 * 用户-认证申请Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface UserAuthApplyMapper extends BaseMapperPlus<UserAuthApply, UserAuthApplyVo> {


    default LambdaQueryWrapper<UserAuthApply> buildQueryWrapper(UserAuthApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserAuthApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, UserAuthApply::getUserId, bo.getUserId());
        lqw.eq(bo.getType() != null, UserAuthApply::getType, bo.getType());
        lqw.eq(bo.getStatus() != null, UserAuthApply::getAuditStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditDesc()), UserAuthApply::getAuditDesc, bo.getAuditDesc());
        lqw.eq(bo.getAuthUserId() != null, UserAuthApply::getAuthUserId, bo.getAuthUserId());
        lqw.eq(bo.getAuthTime() != null, UserAuthApply::getAuthTime, bo.getAuthTime());
        lqw.eq(StringUtils.isNotBlank(bo.getContentJs()), UserAuthApply::getContentJs, bo.getContentJs());
        return lqw;
    }

    /**
     * 根据用户ID和认证类型查询待认证申请记录
     *
     * @param userId 用户ID
     * @param type   认证类型
     * @return 认证申请记录数量
     */
    default long countActiveByUserIdAndType(Long userId, Integer type) {
        return selectCount(Wrappers.<UserAuthApply>lambdaQuery().eq(UserAuthApply::getUserId, userId).eq(UserAuthApply::getType, type));
    }

    /**
     * 根据用户ID查询所有认证申请
     *
     * @param userId 用户ID
     * @return 认证申请列表
     */
    default List<UserAuthApply> selectByUserId(Long userId) {
        return selectList(Wrappers.<UserAuthApply>lambdaQuery().eq(UserAuthApply::getUserId, userId));
    }
}
