package com.gzhuxn.personals.mapper.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.user.UserAuthPayRecord;
import com.gzhuxn.personals.domain.user.bo.UserAuthPayRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户-实名认证支付记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface UserAuthPayRecordMapper extends BaseMapperPlus<UserAuthPayRecord, UserAuthPayRecordVo> {
    default LambdaQueryWrapper<UserAuthPayRecord> buildQueryWrapper(UserAuthPayRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserAuthPayRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, UserAuthPayRecord::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 根据订单ID查询支付记录
     *
     * @param orderId 订单ID
     * @return 支付记录
     */
    UserAuthPayRecord getByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据ID集合和用户ID查询支付记录列表
     *
     * @param ids    ID集合
     * @param userId 用户ID
     * @return 支付记录列表
     */
    List<UserAuthPayRecord> listByIdsAndUserId(@Param("ids") Collection<Long> ids, @Param("userId") Long userId);

    /**
     * 查询用户指定认证类型的未使用且已支付成功的记录
     *
     * @param userId   用户ID
     * @param authType 认证类型
     * @return 支付记录
     */
    UserAuthPayRecord getUnusedPaidRecord(@Param("userId") Long userId, @Param("authType") Integer authType);

    /**
     * 标记支付记录为已使用
     *
     * @param id 记录ID
     * @return 更新行数
     */
    int markAsUsed(@Param("id") Long id);
}
