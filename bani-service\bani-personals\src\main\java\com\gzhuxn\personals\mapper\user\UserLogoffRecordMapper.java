package com.gzhuxn.personals.mapper.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.user.UserLogoffRecord;
import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;

/**
 * 用户-用户注销记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface UserLogoffRecordMapper extends BaseMapperPlus<UserLogoffRecord, UserLogoffRecordVo> {
    /**
     * 构建查询条件
     *
     * @param bo 用户-用户注销记录
     * @return 查询条件
     */
    default LambdaQueryWrapper<UserLogoffRecord> buildQueryWrapper(UserLogoffRecordBo bo) {
        LambdaQueryWrapper<UserLogoffRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, UserLogoffRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getStatus() != null, UserLogoffRecord::getStatus, bo.getStatus());
        lqw.eq(bo.getLogoffTime() != null, UserLogoffRecord::getLogoffTime, bo.getLogoffTime());
        lqw.eq(bo.getSucceedTime() != null, UserLogoffRecord::getSucceedTime, bo.getSucceedTime());
        return lqw;
    }
}
