package com.gzhuxn.personals.mapper.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.user.UserSignIn;
import com.gzhuxn.personals.domain.user.bo.UserSignInBo;
import com.gzhuxn.personals.domain.user.vo.UserSignInVo;

/**
 * 用户-签到Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface UserSignInMapper extends BaseMapperPlus<UserSignIn, UserSignInVo> {
    /**
     * 构建查询条件
     */
    default LambdaQueryWrapper<UserSignIn> buildQueryWrapper(UserSignInBo bo) {
        LambdaQueryWrapper<UserSignIn> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, UserSignIn::getUserId, bo.getUserId());
        lqw.eq(bo.getDate() != null, UserSignIn::getDate, bo.getDate());
        lqw.eq(bo.getConsecutiveDays() != null, UserSignIn::getConsecutiveDays, bo.getConsecutiveDays());
        lqw.eq(bo.getCoin() != null, UserSignIn::getCoin, bo.getCoin());
        return lqw;
    }
}
