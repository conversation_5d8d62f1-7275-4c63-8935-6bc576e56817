package com.gzhuxn.personals.mapper.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.user.UserWithdraw;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawBo;
import com.gzhuxn.personals.domain.user.vo.UserWithdrawVo;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户-提现申请Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface UserWithdrawMapper extends BaseMapperPlus<UserWithdraw, UserWithdrawVo> {
    default LambdaQueryWrapper<UserWithdraw> buildQueryWrapper(UserWithdrawBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserWithdraw> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, UserWithdraw::getUserId, bo.getUserId());
        lqw.eq(bo.getAmount() != null, UserWithdraw::getAmount, bo.getAmount());
        lqw.eq(bo.getWithdrawQrCodeImage() != null, UserWithdraw::getWithdrawQrCodeImage, bo.getWithdrawQrCodeImage());
        lqw.eq(bo.getAuditStatus() != null, UserWithdraw::getAuditStatus, bo.getAuditStatus());
        lqw.eq(bo.getAuditTime() != null, UserWithdraw::getAuditTime, bo.getAuditTime());
        lqw.eq(bo.getAuditUserId() != null, UserWithdraw::getAuditUserId, bo.getAuditUserId());
        lqw.eq(bo.getRemitStatus() != null, UserWithdraw::getRemitStatus, bo.getRemitStatus());
        lqw.eq(bo.getRemitTime() != null, UserWithdraw::getRemitTime, bo.getRemitTime());
        return lqw;
    }

    default Collection<Long> listIdsByIdsAndUserId(Collection<Long> ids, Long userId) {
        return selectList(Wrappers.<UserWithdraw>lambdaQuery()
            .select(UserWithdraw::getId)
            .eq(UserWithdraw::getUserId, userId)
            .in(UserWithdraw::getId, ids))
            .stream().map(UserWithdraw::getId).toList();
    }

    /**
     * 查询用户今日提现次数
     *
     * @param userId 用户ID
     * @param today  今日日期
     * @return 提现次数
     */
    default long countTodayWithdraw(Long userId, LocalDate today) {
        return selectCount(Wrappers.<UserWithdraw>lambdaQuery()
            .eq(UserWithdraw::getUserId, userId)
            .apply("DATE(create_time) = {0}", today));
    }

    /**
     * 查询用户待审核的提现申请
     *
     * @param userId 用户ID
     * @return 待审核的提现申请列表
     */
    default List<UserWithdraw> selectPendingWithdraws(Long userId) {
        return selectList(Wrappers.<UserWithdraw>lambdaQuery()
            .eq(UserWithdraw::getUserId, userId)
            .eq(UserWithdraw::getAuditStatus, 1) // 发起申请状态
            .orderByDesc(UserWithdraw::getCreateTime));
    }

    /**
     * 查询指定状态的提现申请
     *
     * @param auditStatus 审核状态
     * @return 提现申请列表
     */
    default List<UserWithdraw> selectByAuditStatus(Integer auditStatus) {
        return selectList(Wrappers.<UserWithdraw>lambdaQuery()
            .eq(UserWithdraw::getAuditStatus, auditStatus)
            .orderByAsc(UserWithdraw::getCreateTime));
    }

    /**
     * 查询用户的提现历史
     *
     * @param userId 用户ID
     * @return 提现历史列表
     */
    default List<UserWithdraw> selectUserWithdrawHistory(Long userId) {
        return selectList(Wrappers.<UserWithdraw>lambdaQuery()
            .eq(UserWithdraw::getUserId, userId)
            .orderByDesc(UserWithdraw::getCreateTime));
    }
}
