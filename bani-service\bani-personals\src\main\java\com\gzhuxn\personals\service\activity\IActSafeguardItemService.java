package com.gzhuxn.personals.service.activity;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardItemBo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;

import java.util.Collection;
import java.util.List;

/**
 * 活动-活动保障项Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IActSafeguardItemService {
    /**
     * 根据活动保障ID查询活动保障项列表
     *
     * @param safeguardId 活动保障ID
     * @return 活动保障项列表
     */
    List<ActSafeguardItem> listBySafeguardId(Long safeguardId);

    /**
     * 查询活动-活动保障项
     *
     * @param id 主键
     * @return 活动-活动保障项
     */
    ActSafeguardItemVo queryById(Long id);

    /**
     * 分页查询活动-活动保障项列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-活动保障项分页列表
     */
    TableDataInfo<ActSafeguardItemVo> queryPageList(ActSafeguardItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的活动-活动保障项列表
     *
     * @param bo 查询条件
     * @return 活动-活动保障项列表
     */
    List<ActSafeguardItemVo> queryList(ActSafeguardItemBo bo);

    /**
     * 新增活动-活动保障项
     *
     * @param bo 活动-活动保障项
     * @return 是否新增成功
     */
    boolean insertByBo(ActSafeguardItemBo bo);

    /**
     * 修改活动-活动保障项
     *
     * @param bo 活动-活动保障项
     * @return 是否修改成功
     */
    boolean updateByBo(ActSafeguardItemBo bo);

    /**
     * 校验并批量删除活动-活动保障项信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
