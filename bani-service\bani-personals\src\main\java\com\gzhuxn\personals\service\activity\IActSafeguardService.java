package com.gzhuxn.personals.service.activity;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.admin.activity.bo.AdminActivitySafeguardBo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardPageVo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardVo;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardVo;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 活动-活动保障Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IActSafeguardService {

    /**
     * 查询活动-活动保障
     *
     * @param id 主键
     * @return 活动-活动保障
     */
    ActSafeguardVo queryById(Long id);

    /**
     * 查询活动-活动保障详情
     *
     * @param id 主键
     * @return 活动-活动保障
     */
    AdminActivitySafeguardVo getAdminDetail(@NotNull(message = "主键不能为空") Long id);

    /**
     * 分页查询活动-活动保障列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-活动保障分页列表
     */
    TableDataInfo<AdminActivitySafeguardPageVo> queryAdminPageList(ActSafeguardBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的活动-活动保障列表
     *
     * @param bo 查询条件
     * @return 活动-活动保障列表
     */
    List<ActSafeguardVo> queryList(ActSafeguardBo bo);

    /**
     * 新增活动-活动保障
     *
     * @param bo 活动-活动保障
     * @return 是否新增成功
     */
    boolean insertByBo(AdminActivitySafeguardBo bo);

    /**
     * 修改活动-活动保障
     *
     * @param bo 活动-活动保障
     * @return 是否修改成功
     */
    boolean updateByBo(AdminActivitySafeguardBo bo);

    /**
     * 校验并批量删除活动-活动保障信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据主键查询活动-活动保障
     *
     * @param id 主键
     * @return 活动-活动保障
     */
    ActSafeguardStorageVo getStorageVoById(Long id);
}
