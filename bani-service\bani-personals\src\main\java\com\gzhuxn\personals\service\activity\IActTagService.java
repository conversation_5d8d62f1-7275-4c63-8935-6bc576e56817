package com.gzhuxn.personals.service.activity;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.admin.activity.bo.AdminActTagStatusUpBo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActTagVo;
import com.gzhuxn.personals.domain.activity.bo.ActTagBo;
import com.gzhuxn.personals.domain.activity.vo.ActTagVo;

import java.util.Collection;
import java.util.List;

/**
 * 活动-话题管理Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IActTagService {

    /**
     * 查询活动-话题管理
     *
     * @param id 主键
     * @return 活动-话题管理
     */
    AdminActTagVo queryAdminById(Long id);

    /**
     * 分页查询活动-话题管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-话题管理分页列表
     */
    TableDataInfo<AdminActTagVo> queryAdminPageList(ActTagBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的活动-话题管理列表
     *
     * @param bo 查询条件
     * @return 活动-话题管理列表
     */
    List<ActTagVo> queryList(ActTagBo bo);

    /**
     * 新增活动-话题管理
     *
     * @param bo 活动-话题管理
     * @return 是否新增成功
     */
    boolean insertByBo(ActTagBo bo);

    /**
     * 修改活动-话题管理
     *
     * @param bo 活动-话题管理
     * @return 是否修改成功
     */
    boolean updateByBo(ActTagBo bo);

    /**
     * 修改活动-话题管理状态
     *
     * @param bo 活动-话题管理
     * @return 是否修改成功
     */
    boolean updateStatus(AdminActTagStatusUpBo bo);

    /**
     * 校验并批量删除活动-话题管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
