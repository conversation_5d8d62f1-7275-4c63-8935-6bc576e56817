package com.gzhuxn.personals.service.activity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardItemBo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;
import com.gzhuxn.personals.mapper.activity.ActSafeguardItemMapper;
import com.gzhuxn.personals.service.activity.IActSafeguardItemService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 活动-活动保障项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class ActSafeguardItemServiceImpl
    extends BaniServiceImpl<ActSafeguardItemMapper, ActSafeguardItem> implements IActSafeguardItemService {

    @Override
    public List<ActSafeguardItem> listBySafeguardId(Long safeguardId) {
        return baseMapper.listBySafeguardId(safeguardId);
    }

    /**
     * 查询活动-活动保障项
     *
     * @param id 主键
     * @return 活动-活动保障项
     */
    @Override
    public ActSafeguardItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询活动-活动保障项列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-活动保障项分页列表
     */
    @Override
    public TableDataInfo<ActSafeguardItemVo> queryPageList(ActSafeguardItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ActSafeguardItem> lqw = buildQueryWrapper(bo);
        Page<ActSafeguardItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动-活动保障项列表
     *
     * @param bo 查询条件
     * @return 活动-活动保障项列表
     */
    @Override
    public List<ActSafeguardItemVo> queryList(ActSafeguardItemBo bo) {
        LambdaQueryWrapper<ActSafeguardItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件
     *
     * @param bo 查询条件
     * @return 查询条件
     */
    private LambdaQueryWrapper<ActSafeguardItem> buildQueryWrapper(ActSafeguardItemBo bo) {
        LambdaQueryWrapper<ActSafeguardItem> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getSafeguardId() != null, ActSafeguardItem::getSafeguardId, bo.getSafeguardId());
        lqw.eq(null != bo.getIcon(), ActSafeguardItem::getIcon, bo.getIcon());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ActSafeguardItem::getName, bo.getName());
        lqw.like(StringUtils.isNotBlank(bo.getDes()), ActSafeguardItem::getDes, bo.getDes());
        return lqw;
    }

    /**
     * 新增活动-活动保障项
     *
     * @param bo 活动-活动保障项
     * @return 是否新增成功
     */
    @Override
    public boolean insertByBo(ActSafeguardItemBo bo) {
        ActSafeguardItem add = MapstructUtils.convert(bo, ActSafeguardItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改活动-活动保障项
     *
     * @param bo 活动-活动保障项
     * @return 是否修改成功
     */
    @Override
    public boolean updateByBo(ActSafeguardItemBo bo) {
        ActSafeguardItem update = MapstructUtils.convert(bo, ActSafeguardItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ActSafeguardItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除活动-活动保障项信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
