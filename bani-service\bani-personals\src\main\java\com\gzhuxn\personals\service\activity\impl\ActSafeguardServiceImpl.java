package com.gzhuxn.personals.service.activity.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.controller.admin.activity.bo.AdminActivitySafeguardBo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardItemVo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardPageVo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardVo;
import com.gzhuxn.personals.domain.activity.ActSafeguard;
import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBo;
import com.gzhuxn.personals.domain.activity.bo.ActSafeguardItemBo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemStorageVo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardVo;
import com.gzhuxn.personals.mapper.activity.ActSafeguardMapper;
import com.gzhuxn.personals.service.activity.IActSafeguardItemService;
import com.gzhuxn.personals.service.activity.IActSafeguardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 活动-活动保障Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class ActSafeguardServiceImpl
    extends BaniServiceImpl<ActSafeguardMapper, ActSafeguard> implements IActSafeguardService {
    private final IActSafeguardItemService actSafeguardItemService;
    private final IActSafeguardItemService activitySafeguardItemService;

    /**
     * 查询活动-活动保障
     *
     * @param id 主键
     * @return 活动-活动保障
     */
    @Override
    public ActSafeguardVo queryById(Long id) {
        return baseMapper.selectVoById(id, AdminActivitySafeguardVo.class);
    }

    @Override
    public AdminActivitySafeguardVo getAdminDetail(Long id) {
        ActSafeguard safeguard = existById(id);
        AdminActivitySafeguardVo retVo = BeanUtil.copyProperties(safeguard, AdminActivitySafeguardVo.class);

        // 获取保障项目明细列表
        if (retVo != null) {
            ActSafeguardItemBo queryBo = new ActSafeguardItemBo();
            queryBo.setSafeguardId(id);
            List<ActSafeguardItemVo> items = activitySafeguardItemService.queryList(queryBo);
            retVo.setItems(BeanUtil.copyToList(items, AdminActivitySafeguardItemVo.class));
        }

        return retVo;
    }

    /**
     * 分页查询活动-活动保障列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-活动保障分页列表
     */
    @Override
    public TableDataInfo<AdminActivitySafeguardPageVo> queryAdminPageList(ActSafeguardBo bo, PageQuery pageQuery) {
        Page<AdminActivitySafeguardPageVo> result = baseMapper.queryAdminPageList(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动-活动保障列表
     *
     * @param bo 查询条件
     * @return 活动-活动保障列表
     */
    @Override
    public List<ActSafeguardVo> queryList(ActSafeguardBo bo) {
        LambdaQueryWrapper<ActSafeguard> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增活动-活动保障
     *
     * @param bo 活动-活动保障
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(AdminActivitySafeguardBo bo) {
        // 1. 保存保障项目主表
        ActSafeguard add = BeanUtil.copyProperties(bo, ActSafeguard.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());

            // 2. 保存保障项目明细
            if (bo.getItems() != null && !bo.getItems().isEmpty()) {
                for (ActSafeguardItemBo itemBo : bo.getItems()) {
                    itemBo.setSafeguardId(add.getId());
                    actSafeguardItemService.insertByBo(itemBo);
                }
            }
        }
        return flag;
    }

    /**
     * 修改活动-活动保障
     *
     * @param bo 活动-活动保障
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(AdminActivitySafeguardBo bo) {
        // 1. 更新保障项目主表
        ActSafeguard update = BeanUtil.copyProperties(bo, ActSafeguard.class);
        validEntityBeforeSave(update);
        boolean flag = updateById(update);

        if (flag && bo.getItems() != null && !bo.getItems().isEmpty()) {
            // 2. 先删除原有的保障项目明细
            ActSafeguardItemBo queryBo = new ActSafeguardItemBo();
            queryBo.setSafeguardId(bo.getId());
            List<ActSafeguardItemVo> existingItems = actSafeguardItemService.queryList(queryBo);
            if (!existingItems.isEmpty()) {
                List<Long> itemIds = existingItems.stream().map(ActSafeguardItemVo::getId).toList();
                actSafeguardItemService.deleteWithValidByIds(itemIds, false);
            }

            // 3. 重新保存保障项目明细
            for (ActSafeguardItemBo itemBo : bo.getItems()) {
                itemBo.setId(null);
                itemBo.setSafeguardId(bo.getId());
                actSafeguardItemService.insertByBo(itemBo);
            }
        }

        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ActSafeguard entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除活动-活动保障信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 非自己创建的活动保障项目，不允许删除
            long count = baseMapper.countByIdsAndUserId(ids, LoginHelper.getUserId());
            AssertUtils.isTrue(count < 0, "非自己创建的活动保障项目，不允许删除");
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public ActSafeguardStorageVo getStorageVoById(Long id) {
        ActSafeguard actSafeguard = existById(id);
        ActSafeguardStorageVo ret = MapstructUtils.convert(actSafeguard, ActSafeguardStorageVo.class);

        List<ActSafeguardItem> items = actSafeguardItemService.listBySafeguardId(id);
        AssertUtils.notEmpty(items, "活动保障明细不存在");
        ret.setItems(MapstructUtils.convert(items, ActSafeguardItemStorageVo.class));
        return ret;
    }
}
