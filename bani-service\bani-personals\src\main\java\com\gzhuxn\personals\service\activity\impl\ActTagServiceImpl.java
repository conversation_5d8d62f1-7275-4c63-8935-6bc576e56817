package com.gzhuxn.personals.service.activity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.admin.activity.bo.AdminActTagStatusUpBo;
import com.gzhuxn.personals.controller.admin.activity.vo.AdminActTagVo;
import com.gzhuxn.personals.domain.activity.ActTag;
import com.gzhuxn.personals.domain.activity.bo.ActTagBo;
import com.gzhuxn.personals.domain.activity.vo.ActTagVo;
import com.gzhuxn.personals.mapper.activity.ActTagMapper;
import com.gzhuxn.personals.service.activity.IActTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 活动-话题管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RequiredArgsConstructor
@Service
public class ActTagServiceImpl extends BaniServiceImpl<ActTagMapper, ActTag> implements IActTagService {

    /**
     * 查询活动-话题管理
     *
     * @param id 主键
     * @return 活动-话题管理
     */
    @Override
    public AdminActTagVo queryAdminById(Long id) {
        return baseMapper.selectVoById(id, AdminActTagVo.class);
    }

    /**
     * 分页查询活动-话题管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-话题管理分页列表
     */
    @Override
    public TableDataInfo<AdminActTagVo> queryAdminPageList(ActTagBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ActTag> lqw = baseMapper.buildQueryWrapper(bo);
        Page<AdminActTagVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AdminActTagVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动-话题管理列表
     *
     * @param bo 查询条件
     * @return 活动-话题管理列表
     */
    @Override
    public List<ActTagVo> queryList(ActTagBo bo) {
        LambdaQueryWrapper<ActTag> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增活动-话题管理
     *
     * @param bo 活动-话题管理
     * @return 是否新增成功
     */
    @Override
    public boolean insertByBo(ActTagBo bo) {
        ActTag add = MapstructUtils.convert(bo, ActTag.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改活动-话题管理
     *
     * @param bo 活动-话题管理
     * @return 是否修改成功
     */
    @Override
    public boolean updateByBo(ActTagBo bo) {
        ActTag update = MapstructUtils.convert(bo, ActTag.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateStatus(AdminActTagStatusUpBo bo) {
        existById(bo.getId(), "活动话题不存在");
        ActTag upTag = new ActTag();
        upTag.setId(bo.getId());
        upTag.setStatus(bo.getStatus());
        return updateById(upTag);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ActTag entity) {
    }

    /**
     * 校验并批量删除活动-话题管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }
}
