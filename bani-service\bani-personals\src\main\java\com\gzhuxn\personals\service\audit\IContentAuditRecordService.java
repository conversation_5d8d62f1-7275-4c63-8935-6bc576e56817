package com.gzhuxn.personals.service.audit;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.audit.ContentAuditRecord;
import com.gzhuxn.personals.domain.audit.bo.ContentAuditBo;
import com.gzhuxn.personals.domain.audit.vo.ContentAuditDetailVo;
import com.gzhuxn.personals.domain.audit.vo.ContentAuditVo;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;

/**
 * 用户-用户详情审核Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IContentAuditRecordService {

    /**
     * 分页查询内容审核列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 内容审核分页列表
     */
    TableDataInfo<ContentAuditVo> queryPageList(ContentAuditBo bo, PageQuery pageQuery);

    /**
     * 获取审核详情
     *
     * @param id        审核记录ID
     * @param auditType 审核类型
     * @return 审核详情
     */
    ContentAuditDetailVo getDetailById(Long id, AuditType auditType);

    /**
     * 创建用户详情审核
     *
     * @param businessId 业务ID
     * @param auditType  审核类型
     * @return 是否成功
     */
    boolean createAudit(Long businessId, AuditType auditType);

    /**
     * 根据ID查询用户详情审核
     *
     * @param id ID
     * @return 用户详情审核
     */
    ContentAuditRecord selectById(Long id);

    /**
     * 审核通过
     *
     * @param auditRecord 用户详情审核
     * @param res         审核结果
     */
    void pass(ContentAuditRecord auditRecord, ContentAuditRes res);

    /**
     * 审核拒绝
     *
     * @param auditRecord 用户详情审核
     * @param res         审核结果
     */
    void reject(ContentAuditRecord auditRecord, ContentAuditRes res);

    /**
     * 转人工审核
     *
     * @param auditRecord 用户详情审核
     */
    void transferUser(ContentAuditRecord auditRecord);

    /**
     * 转人工审核通过
     *
     * @param id 用户详情审核ID
     */
    void transferUserPass(Long id);

    /**
     * 转人工审核拒绝
     *
     * @param id        用户详情审核ID
     * @param auditDesc 审核描述
     */
    void transferUserReject(Long id, String auditDesc);
}
