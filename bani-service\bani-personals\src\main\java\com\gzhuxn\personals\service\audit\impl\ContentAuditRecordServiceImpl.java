package com.gzhuxn.personals.service.audit.impl;

import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.domain.audit.ContentAuditRecord;
import com.gzhuxn.personals.domain.audit.bo.ContentAuditBo;
import com.gzhuxn.personals.domain.audit.vo.ContentAuditDetailVo;
import com.gzhuxn.personals.domain.audit.vo.ContentAuditVo;
import com.gzhuxn.personals.domain.message.bo.MsgContentSendBo;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.message.MessageSubType;
import com.gzhuxn.personals.enums.message.MessageType;
import com.gzhuxn.personals.event.audit.ContentAuditProducer;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessorFactory;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.mapper.audit.ContentAuditMapper;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.message.IMsgContentUserService;
import com.gzhuxn.personals.service.user.IUserAuthApplyService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户-用户详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class ContentAuditRecordServiceImpl extends BaniServiceImpl<ContentAuditMapper, ContentAuditRecord> implements IContentAuditRecordService {
    @Lazy
    @Resource
    private ContentAuditProducer contentAuditProducer;

    @Resource
    private IMsgContentUserService msgContentUserService;

    @Lazy
    @Resource
    private IUserAuthApplyService userAuthApplyService;
    @Lazy
    @Resource
    private ContentAuditProcessorFactory processorFactory;

    @Override
    public TableDataInfo<ContentAuditVo> queryPageList(ContentAuditBo bo, PageQuery pageQuery) {
        return null;
    }

    @Override
    public ContentAuditDetailVo getDetailById(Long id, AuditType auditType) {
        return null;
    }

    @Override
    public boolean createAudit(Long businessId, AuditType auditType) {
        ContentAuditRecord auditRecord = new ContentAuditRecord();
        auditRecord.setCreateBy(LoginHelper.getUserId());
        auditRecord.setUserId(LoginHelper.getUserId());
        auditRecord.setBusinessId(businessId);
        auditRecord.setType(auditType.getValue());
        auditRecord.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        contentAuditProducer.send(auditRecord, auditType);
        return save(auditRecord);
    }

    @Override
    public ContentAuditRecord selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public void pass(ContentAuditRecord auditRecord, ContentAuditRes res) {
        auditRecord.setAuditTime(LocalDateTime.now());
        auditRecord.setAuditStatus(AuditStatus.PASS.getValue());
        updateById(auditRecord);
        // 发送消息
        MsgContentSendBo sendBo = buildSendMsg(auditRecord, res);
        sendBo.setSubType(MessageSubType.RELATED_AUDIT_SUCCESS);
        msgContentUserService.send(sendBo);
    }

    @Override
    public void reject(ContentAuditRecord auditRecord, ContentAuditRes res) {
        auditRecord.setAuditTime(LocalDateTime.now());
        auditRecord.setAuditStatus(AuditStatus.REJECT.getValue());
        updateById(auditRecord);

        // 发送消息
        MsgContentSendBo sendBo = buildSendMsg(auditRecord, res);
        sendBo.setSubType(MessageSubType.RELATED_AUDIT_REJECT);
        msgContentUserService.send(sendBo);
    }

    @Override
    public void transferUser(ContentAuditRecord auditRecord) {
        // 转人工审核 后续实现推送审核功能
        auditRecord.setAuditStatus(AuditStatus.TRANSFER_WAIT_AUDIT.getValue());
        updateById(auditRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferUserPass(Long id) {
        ContentAuditRecord auditRecord = existById(id);
        ContentAuditRes res = ContentAuditRes.isOk();
        ContentAuditProcessor processor = processorFactory.exitProcessor(auditRecord.getType());
        //  处理
        processor.pass(auditRecord.getBusinessId());
        pass(auditRecord, res);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferUserReject(Long id, String auditDesc) {
        ContentAuditRes res = ContentAuditRes.reject(auditDesc);
        ContentAuditRecord auditRecord = existById(id);

        ContentAuditProcessor processor = processorFactory.exitProcessor(auditRecord.getType());
        //  处理
        processor.reject(auditRecord.getBusinessId(), auditDesc);
        reject(auditRecord, res);
    }

    /**
     * 构建发送消息
     */
    private MsgContentSendBo buildSendMsg(ContentAuditRecord auditRecord, ContentAuditRes res) {
        MsgContentSendBo sendBo = new MsgContentSendBo();
        // 发送消息
        sendBo.setToUserId(auditRecord.getCreateBy());
        sendBo.setType(MessageType.RELATED);
        sendBo.setContent(res.getMessage());
        sendBo.addAllParams(res.getParams());
        return sendBo;
    }
}
