package com.gzhuxn.personals.service.audit.processor;

import com.gzhuxn.personals.domain.user.UserComment;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.user.IUserCommentService;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.domain.RemoteCheckText;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 评论审核处理器
 * <p>
 * 处理用户评论的内容审核
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AuditCommentProcessor implements ContentAuditProcessor {

    @DubboReference
    private RemoteContentAuditService remoteContentAuditService;

    @Resource
    private IUserCommentService userCommentService;

    @Override
    public AuditType getAuditType() {
        return AuditType.COMMENT;
    }

    @Override
    public ContentAuditRes execute(Long businessId) {
        log.info("开始审核评论，评论ID={}", businessId);
        // 查询评论信息
        UserComment comment = userCommentService.existById(businessId);
        try {
            log.info("检查评论内容，评论ID={}，用户ID={}", comment.getId(), comment.getUserId());

            // 使用远程内容审核服务检查文本内容
            RemoteCheckText checkText = remoteContentAuditService.checkText(comment.getContent());

            if (checkText.isPass()) {
                // 审核通过，发布评论
                log.info("评论审核通过，评论ID={}", comment.getId());
                publishComment(comment);
                return ContentAuditRes.isOk();
            } else {
                // 审核不通过，拒绝发布
                String failMsg = checkText.failMag();
                log.info("评论审核不通过，评论ID={}，原因：{}", comment.getId(), failMsg);
                rejectComment(comment);
                return ContentAuditRes.reject(failMsg);
            }
        } catch (Exception e) {
            log.error("审核评论失败，评论ID={}，错误信息：{}", businessId, e.getMessage(), e);
            transferToManualAudit(comment);
            return ContentAuditRes.transferUser();
        }
    }

    @Override
    public void pass(Long businessId) {
        UserComment comment = userCommentService.existById(businessId);
        publishComment(comment);
    }

    @Override
    public void reject(Long businessId, String reason) {
        UserComment comment = userCommentService.existById(businessId);
        rejectComment(comment);
    }

    /**
     * 发布评论
     *
     * @param comment 评论信息
     */
    private void publishComment(UserComment comment) {
        try {
            userCommentService.publishComment(comment);
            log.info("评论发布成功，评论ID={}", comment.getId());
        } catch (Exception e) {
            log.error("发布评论失败，评论ID={}，错误信息：{}", comment.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 拒绝评论
     *
     * @param comment 评论信息
     */
    private void rejectComment(UserComment comment) {
        try {
            userCommentService.rejectComment(comment);
            log.info("评论审核拒绝，评论ID={}", comment.getId());
        } catch (Exception e) {
            log.error("拒绝评论失败，评论ID={}，错误信息：{}", comment.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 转人工审核
     *
     * @param comment 评论信息
     */
    private void transferToManualAudit(UserComment comment) {
        try {
            userCommentService.transferToManualAudit(comment);
            log.info("评论转人工审核，评论ID={}", comment.getId());
        } catch (Exception e) {
            log.error("转人工审核失败，评论ID={}，错误信息：{}", comment.getId(), e.getMessage(), e);
            throw e;
        }
    }
}
