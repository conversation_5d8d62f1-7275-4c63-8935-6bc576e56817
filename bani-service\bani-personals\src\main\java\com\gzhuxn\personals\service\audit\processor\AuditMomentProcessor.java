package com.gzhuxn.personals.service.audit.processor;

import com.gzhuxn.personals.domain.user.UserMoment;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.user.IUserMomentService;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.domain.RemoteCheckText;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 动态审核处理器
 * <p>
 * 处理用户动态的内容审核
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AuditMomentProcessor implements ContentAuditProcessor {

    @DubboReference
    private RemoteContentAuditService remoteContentAuditService;

    @Resource
    private IUserMomentService userMomentService;

    @Override
    public AuditType getAuditType() {
        return AuditType.MOMENT;
    }

    @Override
    public ContentAuditRes execute(Long businessId) {
        log.info("开始审核动态，动态ID={}", businessId);

        try {
            // 查询动态信息
            UserMoment moment = userMomentService.getById(businessId);
            if (moment == null) {
                log.warn("动态不存在，动态ID={}", businessId);
                return ContentAuditRes.reject("动态不存在");
            }

            return checkMoment(moment);
        } catch (Exception e) {
            log.error("审核动态失败，动态ID={}，错误信息：{}", businessId, e.getMessage(), e);
            return ContentAuditRes.transferUser();
        }
    }

    /**
     * 检查动态内容
     *
     * @param moment 动态信息
     * @return 审核结果
     */
    private ContentAuditRes checkMoment(UserMoment moment) {
        log.info("检查动态内容，动态ID={}，用户ID={}", moment.getId(), moment.getUserId());

        try {
            // 使用远程内容审核服务检查文本内容
            RemoteCheckText checkText = remoteContentAuditService.checkText(moment.getContent());

            if (checkText.isPass()) {
                // 审核通过，发布动态
                log.info("动态审核通过，动态ID={}", moment.getId());
                pass(moment.getId());
                return ContentAuditRes.isOk();
            } else {
                // 审核不通过，拒绝发布
                String failMsg = checkText.failMag();
                log.info("动态审核不通过，动态ID={}，原因：{}", moment.getId(), failMsg);
                reject(moment.getId(), failMsg);
                return ContentAuditRes.reject(failMsg);
            }
        } catch (Exception e) {
            log.error("检查动态内容失败，动态ID={}，错误信息：{}", moment.getId(), e.getMessage(), e);
            // 审核异常，转人工审核
            return ContentAuditRes.transferUser();
        }
    }


    @Override
    public void pass(Long businessId) {
        userMomentService.pass(businessId);
    }

    @Override
    public void reject(Long businessId, String reason) {
        userMomentService.reject(businessId, reason);
    }

    @Override
    public void transferUser(Long businessId) {
        userMomentService.transferUser(businessId);
    }
}
