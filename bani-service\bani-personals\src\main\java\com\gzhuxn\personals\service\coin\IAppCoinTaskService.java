package com.gzhuxn.personals.service.coin;

import com.gzhuxn.personals.controller.app.coin.vo.AppCoinTaskVo;

import java.util.List;

/**
 * App端花瓣任务服务接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IAppCoinTaskService {

    /**
     * 查询用户的花瓣任务列表
     *
     * @param userId 用户ID
     * @return 花瓣任务列表
     */
    List<AppCoinTaskVo> queryUserTaskList(Long userId);

    /**
     * 查询指定类型的花瓣任务列表
     *
     * @param userId 用户ID
     * @param type   任务类型;1.签到 2.新手任务
     * @return 花瓣任务列表
     */
    List<AppCoinTaskVo> queryUserTaskListByType(Long userId, Integer type);
}
