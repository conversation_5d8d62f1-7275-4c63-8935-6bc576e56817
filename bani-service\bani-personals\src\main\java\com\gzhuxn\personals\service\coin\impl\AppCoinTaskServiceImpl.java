package com.gzhuxn.personals.service.coin.impl;

import com.gzhuxn.personals.controller.app.coin.vo.AppCoinTaskVo;
import com.gzhuxn.personals.domain.coin.vo.CoinManageVo;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.coin.CoinMgSubType;
import com.gzhuxn.personals.enums.coin.CoinMgType;
import com.gzhuxn.personals.enums.coin.CoinTaskStatus;
import com.gzhuxn.personals.mapper.user.UserAuthApplyMapper;
import com.gzhuxn.personals.service.coin.IAppCoinTaskService;
import com.gzhuxn.personals.service.coin.ICoinManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * App端花瓣任务服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppCoinTaskServiceImpl implements IAppCoinTaskService {

    private final ICoinManageService coinManageService;
    private final UserAuthApplyMapper userAuthApplyMapper;

    @Override
    public List<AppCoinTaskVo> queryUserTaskList(Long userId) {
        // 获取所有任务配置
        List<CoinManageVo> allTasks = coinManageService.queryAllTasks();

        // 获取用户认证状态
        Map<Integer, UserAuthApply> userAuthMap = getUserAuthStatusMap(userId);

        // 转换为App端任务VO
        List<AppCoinTaskVo> taskList = new ArrayList<>();
        for (CoinManageVo coinManage : allTasks) {
            AppCoinTaskVo taskVo = convertToAppTaskVo(coinManage, userAuthMap);
            taskList.add(taskVo);
        }

        return taskList;
    }

    @Override
    public List<AppCoinTaskVo> queryUserTaskListByType(Long userId, Integer type) {
        List<AppCoinTaskVo> allTasks = queryUserTaskList(userId);
        return allTasks.stream()
            .filter(task -> task.getType().equals(type))
            .collect(Collectors.toList());
    }

    /**
     * 获取用户认证状态映射
     *
     * @param userId 用户ID
     * @return 认证状态映射 key:认证类型 value:认证申请
     */
    private Map<Integer, UserAuthApply> getUserAuthStatusMap(Long userId) {
        List<UserAuthApply> userAuthList = userAuthApplyMapper.selectByUserId(userId);
        return userAuthList.stream()
            .collect(Collectors.toMap(
                UserAuthApply::getType,
                auth -> auth,
                (existing, replacement) -> {
                    // 如果有多个相同类型的认证，保留最新的
                    return existing.getCreateTime().isAfter(replacement.getCreateTime()) ? existing : replacement;
                }
            ));
    }

    /**
     * 转换为App端任务VO
     *
     * @param coinManage  金币配置
     * @param userAuthMap 用户认证状态映射
     * @return App端任务VO
     */
    private AppCoinTaskVo convertToAppTaskVo(CoinManageVo coinManage, Map<Integer, UserAuthApply> userAuthMap) {
        AppCoinTaskVo taskVo = new AppCoinTaskVo();
        taskVo.setId(coinManage.getId());
        taskVo.setType(coinManage.getType());
        taskVo.setSubType(coinManage.getSubType());
        taskVo.setName(coinManage.getName());
        taskVo.setCoin(coinManage.getCoin());

        // 设置类型描述
        try {
            CoinMgType mgType = CoinMgType.of(coinManage.getType());
            taskVo.setTypeDesc(mgType.getDesc());
        } catch (Exception e) {
            taskVo.setTypeDesc("未知类型");
        }

        // 设置子类型描述
        try {
            CoinMgSubType mgSubType = CoinMgSubType.of(coinManage.getSubType());
            taskVo.setSubTypeDesc(mgSubType.getDesc());
        } catch (Exception e) {
            taskVo.setSubTypeDesc("未知子类型");
        }

        // 设置任务状态和描述
        setTaskStatus(taskVo, coinManage, userAuthMap);

        // 设置任务描述和图标
        setTaskDescriptionAndIcon(taskVo, coinManage);

        // 设置排序
        taskVo.setSort(calculateSort(coinManage));

        return taskVo;
    }

    /**
     * 设置任务状态
     *
     * @param taskVo      任务VO
     * @param coinManage  金币配置
     * @param userAuthMap 用户认证状态映射
     */
    private void setTaskStatus(AppCoinTaskVo taskVo, CoinManageVo coinManage, Map<Integer, UserAuthApply> userAuthMap) {
        CoinTaskStatus status = CoinTaskStatus.INCOMPLETE;
        boolean canClaim = false;

        // 判断任务类型
        if (CoinMgType.NOVICE_TASK.getValue().equals(coinManage.getType())) {
            // 新手任务 - 认证类型
            Integer authType = getAuthTypeBySubType(coinManage.getSubType());
            if (authType != null) {
                UserAuthApply userAuth = userAuthMap.get(authType);
                if (userAuth != null) {
                    AuditStatus auditStatus = AuditStatus.of(userAuth.getAuditStatus());
                    if (AuditStatus.PASS == auditStatus) {
                        // // 认证通过即表示已领取奖励
                        status = CoinTaskStatus.CLAIMED;
                    } else if (AuditStatus.WAIT_AUDIT == auditStatus || AuditStatus.TRANSFER_WAIT_AUDIT == auditStatus) {
                        // 已提交认证，等待审核
                        status = CoinTaskStatus.COMPLETED;
                    }
                }
            }
        } else if (CoinMgType.SIGN_IN.getValue().equals(coinManage.getType())) {
            // 签到任务 - 这里可以根据实际签到逻辑来判断状态
            // 暂时设置为未完成，后续可以扩展签到功能
            status = CoinTaskStatus.INCOMPLETE;
        }

        taskVo.setStatus(status.getValue());
        taskVo.setStatusDesc(status.getDesc());
        taskVo.setCanClaim(canClaim);
    }

    /**
     * 根据子类型获取认证类型
     *
     * @param subType 子类型
     * @return 认证类型
     */
    private Integer getAuthTypeBySubType(Integer subType) {
        try {
            CoinMgSubType mgSubType = CoinMgSubType.of(subType);
            return switch (mgSubType) {
                case AUTH_IDENTITY -> AuditType.AUTH_APPLY_IDENTITY.getValue();
                case AUTH_EDUCATION -> AuditType.AUTH_APPLY_EDUCATION.getValue();
                case AUTH_VEHICLE -> AuditType.AUTH_APPLY_CAR.getValue();
                case AUTH_HOUSE -> AuditType.AUTH_APPLY_HOUSE.getValue();
                default -> null;
            };
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 设置任务描述和图标
     *
     * @param taskVo     任务VO
     * @param coinManage 金币配置
     */
    private void setTaskDescriptionAndIcon(AppCoinTaskVo taskVo, CoinManageVo coinManage) {
        try {
            CoinMgSubType mgSubType = CoinMgSubType.of(coinManage.getSubType());
            switch (mgSubType) {
                case AUTH_IDENTITY -> {
                    taskVo.setDescription("完成实名认证，获得" + coinManage.getCoin() + "花瓣奖励");
                    taskVo.setIcon("icon_auth_identity");
                }
                case AUTH_EDUCATION -> {
                    taskVo.setDescription("完成学历认证，获得" + coinManage.getCoin() + "花瓣奖励");
                    taskVo.setIcon("icon_auth_education");
                }
                case AUTH_VEHICLE -> {
                    taskVo.setDescription("完成车辆认证，获得" + coinManage.getCoin() + "花瓣奖励");
                    taskVo.setIcon("icon_auth_vehicle");
                }
                case AUTH_HOUSE -> {
                    taskVo.setDescription("完成房屋认证，获得" + coinManage.getCoin() + "花瓣奖励");
                    taskVo.setIcon("icon_auth_house");
                }
                default -> {
                    taskVo.setDescription("完成" + taskVo.getName() + "，获得" + coinManage.getCoin() + "花瓣奖励");
                    taskVo.setIcon("icon_default");
                }
            }
        } catch (Exception e) {
            taskVo.setDescription("完成" + taskVo.getName() + "，获得" + coinManage.getCoin() + "花瓣奖励");
            taskVo.setIcon("icon_default");
        }
    }

    /**
     * 计算排序
     *
     * @param coinManage 金币配置
     * @return 排序值
     */
    private Integer calculateSort(CoinManageVo coinManage) {
        // 类型权重 * 1000 + 子类型
        return coinManage.getType() * 1000 + coinManage.getSubType();
    }
}
