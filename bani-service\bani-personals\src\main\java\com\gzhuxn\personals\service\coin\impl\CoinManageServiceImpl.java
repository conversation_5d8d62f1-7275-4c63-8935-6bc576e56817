package com.gzhuxn.personals.service.coin.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.coin.CoinManage;
import com.gzhuxn.personals.domain.coin.bo.CoinManageBo;
import com.gzhuxn.personals.domain.coin.vo.CoinManageVo;
import com.gzhuxn.personals.mapper.coin.CoinManageMapper;
import com.gzhuxn.personals.service.coin.ICoinManageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 花瓣-花瓣赠送管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@RequiredArgsConstructor
@Service
public class CoinManageServiceImpl extends BaniServiceImpl<CoinManageMapper, CoinManage> implements ICoinManageService {

    /**
     * 查询花瓣-花瓣赠送管理
     *
     * @param id 主键
     * @return 花瓣-花瓣赠送管理
     */
    @Override
    public CoinManageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询花瓣-花瓣赠送管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 花瓣-花瓣赠送管理分页列表
     */
    @Override
    public TableDataInfo<CoinManageVo> queryPageList(CoinManageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CoinManage> lqw = baseMapper.buildQueryWrapper(bo);
        Page<CoinManageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的花瓣-花瓣赠送管理列表
     *
     * @param bo 查询条件
     * @return 花瓣-花瓣赠送管理列表
     */
    @Override
    public List<CoinManageVo> queryList(CoinManageBo bo) {
        LambdaQueryWrapper<CoinManage> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增花瓣-花瓣赠送管理
     *
     * @param bo 花瓣-花瓣赠送管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CoinManageBo bo) {
        CoinManage add = MapstructUtils.convert(bo, CoinManage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改花瓣-花瓣赠送管理
     *
     * @param bo 花瓣-花瓣赠送管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CoinManageBo bo) {
        CoinManage update = MapstructUtils.convert(bo, CoinManage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CoinManage entity) {
    }

    /**
     * 校验并批量删除花瓣-花瓣赠送管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public CoinManageVo queryByTypeAndSubType(Integer type, Integer subType) {
        LambdaQueryWrapper<CoinManage> lqw = Wrappers.lambdaQuery();
        lqw.eq(CoinManage::getType, type);
        lqw.eq(CoinManage::getSubType, subType);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public List<CoinManageVo> queryAllTasks() {
        LambdaQueryWrapper<CoinManage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(CoinManage::getType, CoinManage::getSubType);
        return baseMapper.selectVoList(lqw);
    }
}
