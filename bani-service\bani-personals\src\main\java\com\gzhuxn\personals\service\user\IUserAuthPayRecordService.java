package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.base.domain.pay.ToPayResult;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.app.user.bo.authapply.AuthIdentityPayBo;
import com.gzhuxn.personals.domain.user.bo.UserAuthPayRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;

import java.util.Collection;

/**
 * 用户-实名认证支付记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IUserAuthPayRecordService {

    /**
     * 查询用户-实名认证支付记录
     *
     * @param id 主键
     * @return 用户-实名认证支付记录
     */
    UserAuthPayRecordVo queryById(Long id);

    /**
     * 分页查询用户-实名认证支付记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-实名认证支付记录分页列表
     */
    TableDataInfo<UserAuthPayRecordVo> queryPageList(UserAuthPayRecordBo bo, PageQuery pageQuery);

    /**
     * 实名认证支付
     *
     * @param bo 实名认证支付订单信息
     * @return 支付结果
     */
    ToPayResult authIdentityPay(AuthIdentityPayBo bo);

    /**
     * 校验并批量删除用户-实名认证支付记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查指定认证类型是否需要支付
     *
     * @param userId   用户ID
     * @param authType 认证类型
     * @return true-需要支付，false-不需要支付（已有未使用的支付记录）
     */
    boolean needPayForAuth(Long userId, Integer authType);
}
