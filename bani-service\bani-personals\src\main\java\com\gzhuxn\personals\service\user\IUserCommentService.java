package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.UserComment;
import com.gzhuxn.personals.domain.user.bo.UserCommentBo;
import com.gzhuxn.personals.domain.user.vo.UserCommentVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-评论Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IUserCommentService {

    /**
     * 查询用户-评论
     *
     * @param id 主键
     * @return 用户-评论
     */
    UserCommentVo queryById(Long id);

    /**
     * 分页查询用户-评论列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-评论分页列表
     */
    TableDataInfo<UserCommentVo> queryPageList(UserCommentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-评论列表
     *
     * @param bo 查询条件
     * @return 用户-评论列表
     */
    List<UserCommentVo> queryList(UserCommentBo bo);

    /**
     * 新增用户-评论
     *
     * @param bo 用户-评论
     * @return 是否新增成功
     */
    boolean insertByBo(UserCommentBo bo);

    /**
     * 修改用户-评论
     *
     * @param bo 用户-评论
     * @return 是否修改成功
     */
    boolean updateByBo(UserCommentBo bo);

    /**
     * 校验并批量删除用户-评论信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据id查询用户-评论
     *
     * @param id 主键
     * @return 用户-评论
     */
    UserComment existById(Long id);

    /**
     * 发布评论
     *
     * @param comment 评论
     */
    void publishComment(UserComment comment);

    /**
     * 拒绝评论
     *
     * @param comment 评论
     */
    void rejectComment(UserComment comment);

    /**
     * 转移到人工审核
     *
     * @param comment 评论
     */
    void transferToManualAudit(UserComment comment);
}
