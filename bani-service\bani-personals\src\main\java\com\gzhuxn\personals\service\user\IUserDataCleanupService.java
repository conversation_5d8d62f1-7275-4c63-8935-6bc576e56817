package com.gzhuxn.personals.service.user;

/**
 * 用户数据清理服务接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IUserDataCleanupService {

    /**
     * 清理用户所有相关数据
     *
     * @param userId 用户ID
     */
    void cleanupAllUserData(Long userId);

    /**
     * 清理用户关注关系
     *
     * @param userId 用户ID
     */
    void cleanupUserFollows(Long userId);

    /**
     * 清理用户发布的内容
     *
     * @param userId 用户ID
     */
    void cleanupUserContent(Long userId);

    /**
     * 清理用户聊天记录
     *
     * @param userId 用户ID
     */
    void cleanupUserMessages(Long userId);

    /**
     * 清理用户订单记录
     *
     * @param userId 用户ID
     */
    void cleanupUserOrders(Long userId);

    /**
     * 清理用户认证信息
     *
     * @param userId 用户ID
     */
    void cleanupUserAuth(Long userId);

    /**
     * 清理用户活动参与记录
     *
     * @param userId 用户ID
     */
    void cleanupUserActivities(Long userId);

    /**
     * 清理用户账户信息（需要特殊处理）
     *
     * @param userId 用户ID
     */
    void cleanupUserAccount(Long userId);
}
