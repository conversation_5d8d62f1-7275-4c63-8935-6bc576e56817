package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserAvatarBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserBaseBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserDetailBo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserBaseVo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserDetailVo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserMyProfileVo;
import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.domain.user.bo.UserDetailBo;
import com.gzhuxn.personals.domain.user.vo.UserDetailVo;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户-用户详情Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IUserDetailService {

    /**
     * 查询用户-用户详情
     *
     * @param userId 主键
     * @return 用户-用户详情
     */
    UserDetailVo queryById(Long userId);

    /**
     * 分页查询用户-用户详情列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-用户详情分页列表
     */
    TableDataInfo<UserDetailVo> queryPageList(UserDetailBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-用户详情列表
     *
     * @param bo 查询条件
     * @return 用户-用户详情列表
     */
    List<UserDetailVo> queryList(UserDetailBo bo);

    /**
     * 用户基础信息编辑
     *
     * @param bo 基础信息
     */
    boolean updateBase(AppUserBaseBo bo);

    /**
     * 根据用户id获取用户基础信息
     *
     * @param userId 用户id
     * @return 用户基础信息
     */
    AppUserBaseVo getUserBaseByUserId(Long userId);

    /**
     * 更新用户头像
     *
     * @param bo 头像oss文件id
     * @return 影响行数
     */
    boolean updateUserAvatar(AppUserAvatarBo bo);

    /**
     * 根据用户id获取用户详情
     *
     * @param userId 用户id
     * @return 用户详情
     */
    AppUserDetailVo getDetailByUserId(Long userId);

    /**
     * 用户主页信息编辑
     *
     * @param bo 用户主页信息
     * @return 影响行数
     */
    boolean updateDetail(AppUserDetailBo bo);

    /**
     * 获取用户主页信息
     *
     * @param userId 用户id
     * @return 用户主页信息
     */
    AppUserMyProfileVo getMyProfileByUserId(Long userId);

    /**
     * 根据用户id校验用户是否存在
     *
     * @param userId 用户id
     */
    void validExistByUserId(Long userId);

    /**
     * 获取用户id对应的用户标题
     *
     * @param oppositeUserIds 用户id列表
     * @return 用户昵称map
     */
    Map<Long, String> queryUserTitleMapByUserIds(List<Long> oppositeUserIds);

    /**
     * 根据用户id获取用户详情
     *
     * @param userId 用户id
     * @return 用户详情
     */
    UserDetail getByUserId(Long userId);

    /**
     * 更新用户vip信息
     *
     * @param userId    用户id
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    void updateVip(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 更新用户认证状态
     *
     * @param userId 用户id
     */
    void updateAuthIdentityStatus(Long userId, Boolean isIdentity);

    /**
     * 创建用户pid
     *
     * @return 用户pid
     */
    UserDetail existById(Long userId);
}
