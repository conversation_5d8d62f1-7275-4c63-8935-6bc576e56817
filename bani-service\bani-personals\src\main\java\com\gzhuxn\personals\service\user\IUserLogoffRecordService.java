package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-用户注销记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IUserLogoffRecordService {

    /**
     * 查询用户-用户注销记录
     *
     * @param id 主键
     * @return 用户-用户注销记录
     */
    UserLogoffRecordVo queryById(Long id);

    /**
     * 分页查询用户-用户注销记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-用户注销记录分页列表
     */
    TableDataInfo<UserLogoffRecordVo> queryPageList(UserLogoffRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-用户注销记录列表
     *
     * @param bo 查询条件
     * @return 用户-用户注销记录列表
     */
    List<UserLogoffRecordVo> queryList(UserLogoffRecordBo bo);

    /**
     * 校验并批量删除用户-用户注销记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 申请用户注销
     *
     * @return 是否申请成功
     */
    Boolean applyUserLogoff();

    /**
     * 取消用户注销
     *
     * @param id 注销记录ID
     * @return 是否取消成功
     */
    Boolean cancelUserLogoff(Long id);

    /**
     * 处理用户注销
     *
     * @param logoffRecordId 注销记录ID
     * @param userId         用户ID
     */
    void processUserLogoff(Long logoffRecordId, Long userId);
}
