package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.UserMoment;
import com.gzhuxn.personals.domain.user.bo.UserMomentBo;
import com.gzhuxn.personals.domain.user.vo.UserMomentVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-动态Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IUserMomentService {

    /**
     * 查询用户-动态
     *
     * @param id 主键
     * @return 用户-动态
     */
    UserMomentVo queryById(Long id);

    /**
     * 分页查询用户-动态列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-动态分页列表
     */
    TableDataInfo<UserMomentVo> queryPageList(UserMomentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-动态列表
     *
     * @param bo 查询条件
     * @return 用户-动态列表
     */
    List<UserMomentVo> queryList(UserMomentBo bo);

    /**
     * 新增用户-动态
     *
     * @param bo 用户-动态
     * @return 是否新增成功
     */
    boolean insertByBo(UserMomentBo bo);

    /**
     * 修改用户-动态
     *
     * @param bo 用户-动态
     * @return 是否修改成功
     */
    boolean updateByBo(UserMomentBo bo);

    /**
     * 校验并批量删除用户-动态信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, boolean isValid);

    /**
     * 根据id查询用户-动态
     *
     * @param id 主键
     * @return 用户-动态
     */
    UserMoment getById(Long id);

    /**
     * 发布动态
     *
     * @param id 动态id
     */
    void pass(Long id);

    /**
     * 拒绝动态
     *
     * @param businessId 业务id
     * @param reason     拒绝原因
     */
    void reject(Long businessId, String reason);

    /**
     * 转人工审核动态
     *
     * @param businessId 业务id
     */
    void transferUser(Long businessId);
}
