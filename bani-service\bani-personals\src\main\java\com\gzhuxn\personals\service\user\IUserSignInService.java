package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.bo.UserSignInBo;
import com.gzhuxn.personals.domain.user.vo.UserSignInVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-签到Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IUserSignInService {

    /**
     * 查询用户-签到
     *
     * @param id 主键
     * @return 用户-签到
     */
    UserSignInVo queryById(Long id);

    /**
     * 分页查询用户-签到列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-签到分页列表
     */
    TableDataInfo<UserSignInVo> queryPageList(UserSignInBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-签到列表
     *
     * @param bo 查询条件
     * @return 用户-签到列表
     */
    List<UserSignInVo> queryList(UserSignInBo bo);

    /**
     * 新增用户-签到
     *
     * @param bo 用户-签到
     * @return 是否新增成功
     */
    Boolean insertByBo(UserSignInBo bo);

    /**
     * 修改用户-签到
     *
     * @param bo 用户-签到
     * @return 是否修改成功
     */
    Boolean updateByBo(UserSignInBo bo);

    /**
     * 用户签到
     *
     * @return 签到结果
     */
    UserSignInVo signIn();

    /**
     * 校验并批量删除用户-签到信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
