package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawAuditBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawCreateBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawRemitBo;
import com.gzhuxn.personals.domain.user.vo.UserWithdrawVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-提现申请Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IUserWithdrawService {

    /**
     * 查询用户-提现申请
     *
     * @param id 主键
     * @return 用户-提现申请
     */
    UserWithdrawVo queryById(Long id);

    /**
     * 分页查询用户-提现申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-提现申请分页列表
     */
    TableDataInfo<UserWithdrawVo> queryPageList(UserWithdrawBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-提现申请列表
     *
     * @param bo 查询条件
     * @return 用户-提现申请列表
     */
    List<UserWithdrawVo> queryList(UserWithdrawBo bo);

    /**
     * 新增用户-提现申请
     *
     * @param bo 用户-提现申请
     * @return 是否新增成功
     */
    boolean insertByBo(UserWithdrawBo bo);

    /**
     * 修改用户-提现申请
     *
     * @param bo 用户-提现申请
     * @return 是否修改成功
     */
    boolean updateByBo(UserWithdrawBo bo);

    /**
     * 发起提现申请
     *
     * @param bo 提现申请信息
     * @return 是否申请成功
     */
    boolean createWithdrawApply(UserWithdrawCreateBo bo);

    /**
     * 审核提现申请
     *
     * @param bo 审核信息
     * @return 是否审核成功
     */
    boolean auditWithdraw(UserWithdrawAuditBo bo);

    /**
     * 提现汇款
     *
     * @param bo 汇款信息
     * @return 是否汇款成功
     */
    boolean remitWithdraw(UserWithdrawRemitBo bo);

    /**
     * 校验并批量删除用户-提现申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
