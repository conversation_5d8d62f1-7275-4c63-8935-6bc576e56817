package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.UserAccount;
import com.gzhuxn.personals.domain.user.bo.UserAccountBo;
import com.gzhuxn.personals.domain.user.bo.UserAccountHistoryBo;
import com.gzhuxn.personals.domain.user.bo.UserAccountUpdateBo;
import com.gzhuxn.personals.domain.user.vo.UserAccountVo;
import com.gzhuxn.personals.enums.coin.CoinType;
import com.gzhuxn.personals.enums.user.account.AccountHistoryOpType;
import com.gzhuxn.personals.enums.user.account.AccountHistoryStatus;
import com.gzhuxn.personals.enums.user.account.AccountUpType;
import com.gzhuxn.personals.mapper.user.UserAccountMapper;
import com.gzhuxn.personals.service.user.IUserAccountHistoryService;
import com.gzhuxn.personals.service.user.IUserAccountService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 用户-账户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class UserAccountServiceImpl extends BaniServiceImpl<UserAccountMapper, UserAccount> implements IUserAccountService {
    @Resource
    private IUserAccountHistoryService userAccountHistoryService;

    /**
     * 查询用户-账户信息
     *
     * @param userId 主键
     * @return 用户-账户信息
     */
    @Override
    public UserAccountVo queryById(Long userId) {
        UserAccountVo accountVo = baseMapper.selectVoById(userId);
        if (accountVo != null) {
            // 计算可提现金额
            accountVo.setWithdrawableAmount(calculateWithdrawableAmount(accountVo.getWithdrawCoin()));
        }
        return accountVo;
    }

    /**
     * 计算可提现金额
     * 规则：1元人民币 = 10花瓣金币，提现比例70%，手续费5%
     *
     * @param withdrawCoin 可提现花瓣数
     * @return 可提现金额（元）
     */
    private BigDecimal calculateWithdrawableAmount(Integer withdrawCoin) {
        if (withdrawCoin == null || withdrawCoin <= 0) {
            return BigDecimal.ZERO;
        }

        // 1元 = 10花瓣，所以花瓣转换为元需要除以10
        BigDecimal coinToYuan = new BigDecimal(withdrawCoin).divide(new BigDecimal("10"), 2, RoundingMode.DOWN);

        // 提现比例70%
        BigDecimal withdrawableBase = coinToYuan.multiply(new BigDecimal("0.7"));

        // 扣除手续费5%，实际到账95%
        BigDecimal actualAmount = withdrawableBase.multiply(new BigDecimal("0.95"));

        return actualAmount.setScale(2, RoundingMode.DOWN);
    }

    /**
     * 分页查询用户-账户信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-账户信息分页列表
     */
    @Override
    public TableDataInfo<UserAccountVo> queryPageList(UserAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserAccount> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserAccountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public boolean isEnough(Long userId, int totalCoin) {
        int coin = baseMapper.getCoinByUserId(userId);
        return coin >= totalCoin;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(AccountUpType upType, UserAccountUpdateBo updateBo) {
        UserAccount account = validUpdate(updateBo);
        updateBo.build();
        int coin = updateBo.getCoin();
        CoinType coinType = updateBo.getCoinType();
        // 收入
        if (upType.isIncome()) {
            // 创建收入记录
            saveHistory(upType, account, updateBo);

            // 更新账户信息
            upCoin(account, coin, coinType);
            return updateById(account);
        }
        AccountHistoryStatus status = updateBo.getStatus();
        // 支出
        // 更新支出人账户信息
        saveHistory(upType, account, updateBo, status);

        // 增加锁库花瓣
        if (status.isLock()) {
            upLockCoin(account, coin, coinType);
        }
        // 实际扣除花瓣
        else {
            upCoin(account, -coin, coinType);

            // 减除被锁花瓣
            if (updateBo.isLockDeduct()) {
                upLockCoin(account, -coin, coinType);
            }
        }
        updateById(account);

        // 不需要更新对方账户信息直接返回
        AccountUpType oppositeUpType = upType.getOppositeUpType();
        if (null == oppositeUpType || status.isLock()) {
            return true;
        }
        // 更新对方账户信息
        Long oppositeUserId = updateBo.getOppositeUserId();
        AssertUtils.notNull(oppositeUserId, "对方用户ID不能为空");
        UserAccount oppositeAccount = getAccountByUserId(oppositeUserId);

        saveHistory(oppositeUpType, oppositeAccount, updateBo);

        upCoin(oppositeAccount, coin, coinType);
        return updateById(oppositeAccount);
    }

    /**
     * 更新账户花瓣数
     */
    private void upCoin(UserAccount account, int coin, CoinType coinType) {
        // 可提现
        if (CoinType.WITHDRAW == coinType) {
            account.setWithdrawCoin(account.getWithdrawCoin() + coin);
            return;
        }
        account.setCoin(account.getCoin() + coin);
    }

    /**
     * 更新锁定花瓣数
     */
    private void upLockCoin(UserAccount account, int coin, CoinType coinType) {
        // 可提现
        if (CoinType.WITHDRAW == coinType) {
            account.setLockWithdrawCoin(account.getLockWithdrawCoin() + coin);
            return;
        }
        account.setLockCoin(account.getLockCoin() + coin);
    }

    /**
     * 校验更新信息
     *
     * @param updateBo 更新信息
     * @return 用户账户信息
     */
    private UserAccount validUpdate(UserAccountUpdateBo updateBo) {
        // 默认状态为正常
        if (null == updateBo.getStatus()) {
            updateBo.setStatus(AccountHistoryStatus.NORMAL);
        }
        return getAccountByUserId(updateBo.getUserId());
    }

    /**
     * 保存账号历史记录
     *
     * @param upType   收入类型
     * @param updateBo 更新信息
     */
    private void saveHistory(AccountUpType upType, UserAccount account, UserAccountUpdateBo updateBo) {
        saveHistory(upType, account, updateBo, AccountHistoryStatus.NORMAL);
    }

    /**
     * 保存账号历史记录
     *
     * @param upType   收入类型
     * @param updateBo 更新信息
     */
    private void saveHistory(AccountUpType upType, UserAccount account, UserAccountUpdateBo updateBo, AccountHistoryStatus status) {
        UserAccountHistoryBo historyBo = new UserAccountHistoryBo();
        historyBo.setUserId(account.getUserId());
        historyBo.setType(upType.getValue());
        historyBo.setBusinessId(updateBo.getBusinessId());
        historyBo.setCoinType(updateBo.getCoinType().getValue());

        // 变更金额
        historyBo.setUse(updateBo.getCoin());

        // 变更前金额
        historyBo.setUseBefore(account.getCoin());

        // 变更后金额
        int useAfter = account.getCoin();
        if (upType.isIncome()) {
            useAfter += updateBo.getCoin();
            historyBo.setOpType(AccountHistoryOpType.ADD.getValue());
        }
        // 扣除
        else {
            AssertUtils.isTrue(account.getCoin() - account.getLockCoin() <= 0, "{coin.name}不足，请充值！");
            useAfter -= updateBo.getCoin();
            historyBo.setOpType(AccountHistoryOpType.DEDUCT.getValue());
        }
        // 变更后金额,锁定花瓣不产生实际扣除
        historyBo.setUseAfter(status.isLock() ? historyBo.getUseBefore() : useAfter);
        historyBo.setStatus(updateBo.getStatus().getValue());
        userAccountHistoryService.insertByBo(historyBo);
    }

    /**
     * 根据用户ID获取用户账户信息
     *
     * @param userId 用户ID
     * @return 用户账户信息
     */
    private UserAccount getAccountByUserId(Long userId) {
        UserAccount account = getById(userId);
        // 如果用户账户信息不存在，则创建一个默认账户信息
        if (account == null) {
            account = new UserAccount();
            account.setUserId(userId);
            account.setCoin(0);
            account.setLockCoin(0);
            account.setWithdrawCoin(0);
            account.setLockWithdrawCoin(0);
            save(account);

            // 重新获取用户账户信息
            account = getById(userId);
        }
        return account;
    }
}
