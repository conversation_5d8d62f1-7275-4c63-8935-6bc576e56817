package com.gzhuxn.personals.service.user.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.domain.user.UserAuthPayRecord;
import com.gzhuxn.personals.domain.user.bo.UserAuthApplyBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthApplyVo;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.mapper.user.UserAuthApplyMapper;
import com.gzhuxn.personals.mapper.user.UserAuthPayRecordMapper;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.message.IMsgContentUserService;
import com.gzhuxn.personals.service.user.IUserAuthApplyService;
import com.gzhuxn.personals.service.user.IUserAuthRewardService;
import com.gzhuxn.personals.service.user.IUserDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 用户-认证申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserAuthApplyServiceImpl
    extends BaniServiceImpl<UserAuthApplyMapper, UserAuthApply> implements IUserAuthApplyService {
    private final IUserDetailService userDetailService;
    private final IMsgContentUserService msgContentUserService;
    private final IContentAuditRecordService contentAuditRecordService;
    private final UserAuthPayRecordMapper userAuthPayRecordMapper;
    private final IUserAuthRewardService userAuthRewardService;

    /**
     * 查询用户-认证申请
     *
     * @param id 主键
     * @return 用户-认证申请
     */
    @Override
    public UserAuthApplyVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-认证申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-认证申请分页列表
     */
    @Override
    public TableDataInfo<UserAuthApplyVo> queryAdminPageList(UserAuthApplyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserAuthApply> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserAuthApplyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-认证申请列表
     *
     * @param bo 查询条件
     * @return 用户-认证申请列表
     */
    @Override
    public List<UserAuthApplyVo> queryList(UserAuthApplyBo bo) {
        LambdaQueryWrapper<UserAuthApply> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public boolean createIdentity(UserAuthApplyBo.AuthIdentityBo bo) {
        UserAuthApplyBo applyBo = new UserAuthApplyBo();
        applyBo.setUserId(bo.getUserId());
        applyBo.setType(AuditType.AUTH_APPLY_IDENTITY.getValue());
        applyBo.setContentJs(JSON.toJSONString(bo));
        return createApply(applyBo);
    }

    @Override
    public boolean createEducation(UserAuthApplyBo.AuthEducationBo bo) {
        UserAuthApplyBo applyBo = new UserAuthApplyBo();
        applyBo.setUserId(bo.getUserId());
        applyBo.setType(AuditType.AUTH_APPLY_EDUCATION.getValue());
        applyBo.setContentJs(JSON.toJSONString(bo));
        return createApply(applyBo);
    }

    @Override
    public boolean createCar(UserAuthApplyBo.AuthCarBo bo) {
        UserAuthApplyBo applyBo = new UserAuthApplyBo();
        applyBo.setUserId(bo.getUserId());
        applyBo.setType(AuditType.AUTH_APPLY_CAR.getValue());
        applyBo.setContentJs(JSON.toJSONString(bo));
        return createApply(applyBo);
    }

    @Override
    public boolean createHouse(UserAuthApplyBo.AuthHouseBo bo) {
        UserAuthApplyBo applyBo = new UserAuthApplyBo();
        applyBo.setUserId(bo.getUserId());
        applyBo.setType(AuditType.AUTH_APPLY_HOUSE.getValue());
        applyBo.setContentJs(JSON.toJSONString(bo));
        return createApply(applyBo);
    }

    /**
     * 新增用户-认证申请
     *
     * @param bo 用户-认证申请
     * @return 是否新增成功
     */
    private boolean createApply(UserAuthApplyBo bo) {
        // 存在id，则更新
        if (null != bo.getId()) {
            existById(bo.getId(), "认证申请已存在");
        }

        long count = baseMapper.countActiveByUserIdAndType(bo.getUserId(), bo.getType());
        AssertUtils.isTrue(count == 0, "已经申请过该类型的认证");

        // 校验并消费认证支付记录
        boolean payValidated = validateAndConsumePayRecord(bo.getUserId(), bo.getType());
        AssertUtils.isTrue(payValidated, "认证支付记录校验失败");

        UserAuthApply add = MapstructUtils.convert(bo, UserAuthApply.class);
        add.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        // 发送申请消息
        contentAuditRecordService.createAudit(add.getId(), AuditType.of(bo.getType()));
        return flag;
    }

    /**
     * 修改用户-认证申请
     *
     * @param bo 用户-认证申请
     * @return 是否修改成功
     */
    @Override
    public boolean updateByBo(UserAuthApplyBo bo) {
        UserAuthApply update = MapstructUtils.convert(bo, UserAuthApply.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除用户-认证申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void pass(Long id) {
        // 先获取认证申请信息，用于金币赠送
        UserAuthApply existingApply = existById(id);

        UserAuthApply apply = new UserAuthApply();
        apply.setId(id);
        apply.setAuditStatus(AuditStatus.PASS.getValue());
        apply.setAuthTime(LocalDateTime.now());
        apply.setAuthUserId(LoginHelper.getUserId());
        updateById(apply);

        // 认证成功后赠送金币
        try {
            userAuthRewardService.grantAuthSuccessReward(apply);
        } catch (Exception e) {
            // 金币赠送失败不影响认证通过，只记录日志
            log.warn("认证成功金币赠送失败，用户ID: {}, 认证类型: {}", existingApply.getUserId(), existingApply.getType(), e);
        }
    }

    @Override
    public void reject(Long id, String reason) {
        UserAuthApply apply = new UserAuthApply();
        apply.setId(id);
        apply.setAuditStatus(AuditStatus.REJECT.getValue());
        apply.setAuthTime(LocalDateTime.now());
        apply.setAuditDesc(reason);
        apply.setAuthUserId(LoginHelper.getUserId());
        updateById(apply);
    }

    @Override
    public void transferUser(Long businessId) {
        UserAuthApply apply = existById(businessId);
        apply.setAuditStatus(AuditStatus.TRANSFER_WAIT_AUDIT.getValue());
        baseMapper.updateById(apply);
    }

    @Override
    public UserAuthApply existById(Long id) {
        return existById(id, "认证申请不存在");
    }

    @Override
    public boolean validateAndConsumePayRecord(Long userId, Integer authType) {
        // 查询用户指定认证类型的未使用且已支付成功的记录
        UserAuthPayRecord payRecord = userAuthPayRecordMapper.getUnusedPaidRecord(userId, authType);
        AssertUtils.notNull(payRecord, "未找到有效的认证支付记录，请先完成支付");

        // 标记支付记录为已使用
        int updateCount = userAuthPayRecordMapper.markAsUsed(payRecord.getId());
        return updateCount > 0;
    }
}
