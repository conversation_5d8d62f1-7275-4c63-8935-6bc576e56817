package com.gzhuxn.personals.service.user.impl;

import com.gzhuxn.personals.domain.coin.vo.CoinManageVo;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.domain.user.bo.UserAccountUpdateBo;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.coin.CoinMgSubType;
import com.gzhuxn.personals.enums.coin.CoinMgType;
import com.gzhuxn.personals.enums.coin.CoinType;
import com.gzhuxn.personals.enums.user.account.AccountUpType;
import com.gzhuxn.personals.service.coin.ICoinManageService;
import com.gzhuxn.personals.service.user.IUserAccountService;
import com.gzhuxn.personals.service.user.IUserAuthRewardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户认证奖励服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserAuthRewardServiceImpl implements IUserAuthRewardService {

    private final ICoinManageService coinManageService;
    private final IUserAccountService userAccountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean grantAuthSuccessReward(UserAuthApply apply) {
        try {
            // 根据认证类型获取对应的金币配置子类型
            CoinMgSubType subType = getAuthSubType(apply.getType());
            if (subType == null) {
                log.warn("认证类型 {} 没有对应的金币配置", apply.getType());
                return false;
            }

            // 查询金币配置
            CoinManageVo coinConfig = coinManageService.queryByTypeAndSubType(CoinMgType.NOVICE_TASK.getValue(), subType.getValue());
            if (coinConfig == null) {
                log.warn("未找到认证类型 {} 的金币配置", apply.getType());
                return false;
            }

            // 赠送金币
            UserAccountUpdateBo updateBo = UserAccountUpdateBo.builder()
                .userId(apply.getUserId())
                .coin(coinConfig.getCoin())
                .coinType(CoinType.GIFT)
                .businessId(apply.getId())
                .build();

            boolean success = userAccountService.update(AccountUpType.AUTH_REWARD, updateBo);

            if (success) {
                log.info("用户 {} 认证类型 {} 成功，赠送金币 {}", apply.getUserId(), apply.getType(), coinConfig.getCoin());
            } else {
                log.error("用户 {} 认证类型 {} 金币赠送失败", apply.getUserId(), apply.getType());
            }

            return success;
        } catch (Exception e) {
            log.error("用户 {} 认证类型 {} 金币赠送异常", apply.getUserId(), apply.getType(), e);
            return false;
        }
    }

    /**
     * 根据认证类型获取对应的金币配置子类型
     *
     * @param authType 认证类型
     * @return 金币配置子类型
     */
    private CoinMgSubType getAuthSubType(Integer authType) {
        AuditType auditType = AuditType.of(authType);
        return switch (auditType) {
            case AUTH_APPLY_IDENTITY -> CoinMgSubType.AUTH_IDENTITY;
            case AUTH_APPLY_EDUCATION -> CoinMgSubType.AUTH_EDUCATION;
            case AUTH_APPLY_CAR -> CoinMgSubType.AUTH_VEHICLE;
            case AUTH_APPLY_HOUSE -> CoinMgSubType.AUTH_HOUSE;
            default -> null;
        };
    }
}
