package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.domain.user.UserComment;
import com.gzhuxn.personals.domain.user.bo.UserCommentBo;
import com.gzhuxn.personals.domain.user.vo.UserCommentVo;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.mapper.user.UserCommentMapper;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.user.IUserCommentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 用户-评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class UserCommentServiceImpl
    extends BaniServiceImpl<UserCommentMapper, UserComment> implements IUserCommentService {

    private final IContentAuditRecordService contentAuditRecordService;

    /**
     * 查询用户-评论
     *
     * @param id 主键
     * @return 用户-评论
     */
    @Override
    public UserCommentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-评论列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-评论分页列表
     */
    @Override
    public TableDataInfo<UserCommentVo> queryPageList(UserCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserComment> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserCommentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-评论列表
     *
     * @param bo 查询条件
     * @return 用户-评论列表
     */
    @Override
    public List<UserCommentVo> queryList(UserCommentBo bo) {
        LambdaQueryWrapper<UserComment> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增用户-评论
     *
     * @param bo 用户-评论
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(UserCommentBo bo) {
        UserComment add = MapstructUtils.convert(bo, UserComment.class);

        // 设置当前用户ID
        add.setCreateBy(LoginHelper.getUserId());
        add.setUserId(LoginHelper.getUserId());

        // 设置为待审核状态
        add.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            // 发起异步审核
            contentAuditRecordService.createAudit(add.getId(), AuditType.COMMENT);
        }
        return flag;
    }

    /**
     * 修改用户-评论
     *
     * @param bo 用户-评论
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(UserCommentBo bo) {
        UserComment update = MapstructUtils.convert(bo, UserComment.class);

        // 查询原有评论
        UserComment existing = baseMapper.selectById(bo.getId());
        AssertUtils.notNull(existing, "评论不存在");

        // 检查权限：只能修改自己的评论
        AssertUtils.isTrue(existing.getCreateBy().equals(LoginHelper.getUserId()), "无权限修改此评论");

        // 如果内容发生变化，需要重新审核
        if (!existing.getContent().equals(bo.getContent())) {
            update.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        }

        boolean flag = baseMapper.updateById(update) > 0;

        // 如果内容发生变化，发起异步审核
        if (flag && !existing.getContent().equals(bo.getContent())) {
            contentAuditRecordService.createAudit(bo.getId(), AuditType.COMMENT);
        }

        return flag;
    }

    /**
     * 校验并批量删除用户-评论信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查权限：只能删除自己的评论
            Long userId = LoginHelper.getUserId();
            LambdaQueryWrapper<UserComment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(UserComment::getId, ids)
                .eq(UserComment::getCreateBy, userId);
            List<UserComment> userComments = baseMapper.selectList(queryWrapper);
            AssertUtils.notEmpty(userComments, "未找到关联评论");
            ids = userComments.stream().map(UserComment::getId).toList();
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public UserComment existById(Long id) {
        return existById(id, "评论不存在");
    }

    /**
     * 发布评论（审核通过后调用）
     *
     * @param comment 评论信息
     */
    @Override
    public void publishComment(UserComment comment) {
        comment.setAuditStatus(AuditStatus.PASS.getValue());
        baseMapper.updateById(comment);
    }

    /**
     * 拒绝评论（审核不通过后调用）
     *
     * @param comment 评论信息
     */
    @Override
    public void rejectComment(UserComment comment) {
        comment.setAuditStatus(AuditStatus.REJECT.getValue());
        baseMapper.updateById(comment);
    }

    /**
     * 转人工审核
     *
     * @param comment 评论信息
     */
    @Override
    public void transferToManualAudit(UserComment comment) {
        comment.setAuditStatus(AuditStatus.TRANSFER_WAIT_AUDIT.getValue());
        baseMapper.updateById(comment);
    }
}
