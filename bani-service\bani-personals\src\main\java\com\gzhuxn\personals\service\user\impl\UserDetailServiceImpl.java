package com.gzhuxn.personals.service.user.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.core.utils.StreamUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.redis.utils.RedisUtils;
import com.gzhuxn.common.tenant.helper.TenantHelper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserAvatarBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserBaseBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserDetailBo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserBaseVo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserDetailVo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserMyProfileStatCountVo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserMyProfileVo;
import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.domain.user.bo.UserDetailBo;
import com.gzhuxn.personals.domain.user.vo.UserAlbumVo;
import com.gzhuxn.personals.domain.user.vo.UserDetailVo;
import com.gzhuxn.personals.domain.user.vo.UserRequireTagVo;
import com.gzhuxn.personals.domain.user.vo.UserTagVo;
import com.gzhuxn.personals.enums.UserAnimalVal;
import com.gzhuxn.personals.enums.UserStarVal;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.dict.UserJob;
import com.gzhuxn.personals.enums.dict.UserRevenue;
import com.gzhuxn.personals.mapper.user.UserDetailMapper;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.user.IUserAlbumService;
import com.gzhuxn.personals.service.user.IUserDetailService;
import com.gzhuxn.personals.service.user.IUserRequireTagService;
import com.gzhuxn.personals.service.user.IUserTagService;
import com.gzhuxn.personals.utils.UserUtils;
import com.gzhuxn.resource.api.RemoteFileService;
import com.gzhuxn.system.api.RemoteDictService;
import com.gzhuxn.system.api.RemoteUserService;
import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import com.gzhuxn.system.api.domain.vo.RemoteUserVo;
import jakarta.annotation.Resource;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户-用户详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class UserDetailServiceImpl
    extends BaniServiceImpl<UserDetailMapper, UserDetail> implements IUserDetailService {
    private static final String USER_PID_PREFIX = "%s:user:pid:";

    @Resource
    private UserDetailMapper baseMapper;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteFileService remoteFileService;

    @Lazy
    @Resource
    private UserDetailServiceImpl self;

    @Resource
    private IUserAlbumService userAlbumService;

    @Resource
    private IUserTagService userTagService;

    @Resource
    private IUserRequireTagService userRequireTagService;

    @Resource
    private IContentAuditRecordService userDetailAuditService;

    @Resource
    private WxMpService wxMpService;

    /**
     * 查询用户-用户详情
     *
     * @param userId 主键
     * @return 用户-用户详情
     */
    @Override
    public UserDetailVo queryById(Long userId) {
        return baseMapper.selectVoById(userId);
    }

    /**
     * 分页查询用户-用户详情列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-用户详情分页列表
     */
    @Override
    public TableDataInfo<UserDetailVo> queryPageList(UserDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserDetail> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-用户详情列表
     *
     * @param bo 查询条件
     * @return 用户-用户详情列表
     */
    @Override
    public List<UserDetailVo> queryList(UserDetailBo bo) {
        LambdaQueryWrapper<UserDetail> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBase(AppUserBaseBo bo) {
        // 更新基础数据
        RemoteUserBo sysUserBo = MapstructUtils.convert(bo, RemoteUserBo.class);
        sysUserBo.setUserName(bo.getNickName());
        remoteUserService.updateUserInfo(sysUserBo);

        RemoteUserVo remoteUserVo = remoteUserService.selectById(bo.getUserId());
        AssertUtils.isTrue(null != remoteUserVo, "用户不存在");
        // 更新用户详情
        UserDetail userDetail = getById(bo.getUserId());
        boolean addUserDetail = false;
        if (null == userDetail) {
            addUserDetail = true;
            userDetail = new UserDetail();
            userDetail.setUserId(bo.getUserId());
            userDetail.setPid(self.createPid());
        }

        // 更新生日信息
        if (!bo.getBirthday().equals(userDetail.getBirthday())) {
            userDetail.setBirthday(bo.getBirthday());
            userDetail.setStar(UserStarVal.ofValue(userDetail.getBirthday()));
            userDetail.setAnimal(UserAnimalVal.ofValue(userDetail.getBirthday()));
        }
        userDetail.setNickName(bo.getNickName());
        userDetail.setPhoneNumber(remoteUserVo.getPhoneNumber());
        userDetail.setSex(bo.getSex());
        userDetail.setHeight(bo.getHeight());
        userDetail.setEdu(bo.getEdu());
        userDetail.setJob(bo.getJob());
        userDetail.setRevenue(bo.getRevenue());
        if (addUserDetail) {
            return save(userDetail);
        }
        return updateById(userDetail);
    }

    @Override
    public AppUserBaseVo getUserBaseByUserId(Long userId) {
        UserDetail userDetail = baseMapper.selectById(userId);
        AppUserBaseVo returnVo = null;
        if (null != userDetail) {
            returnVo = MapstructUtils.convert(userDetail, AppUserBaseVo.class);
            RemoteUserVo remoteUserVo = remoteUserService.selectById(userId);
            if (null != remoteUserVo) {
                returnVo.setPhonenumber(remoteUserVo.getPhoneNumber());
                returnVo.setSex(remoteUserVo.getSex());
                returnVo.setNickName(remoteUserVo.getNickName());
            }
        }
        return returnVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserAvatar(AppUserAvatarBo bo) {
        RemoteUserBo remoteUserBo = MapstructUtils.convert(bo, RemoteUserBo.class);
        UserDetail userDetail = existById(bo.getUserId());
        UserDetail upDetail = new UserDetail();
        upDetail.setUserId(bo.getUserId());
        upDetail.setAvatar(bo.getAvatar());
        remoteUserService.updateUserAvatar(remoteUserBo);

        userDetailAuditService.createAudit(userDetail.getUserId(), AuditType.USER_BASE_INFO);
        upDetail.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        return updateById(upDetail);
    }

    @Override
    public AppUserDetailVo getDetailByUserId(Long userId) {
        UserDetail userDetail = existById(userId);
        AppUserDetailVo retUser = MapstructUtils.convert(userDetail, AppUserDetailVo.class);
        RemoteUserVo remoteUserVo = remoteUserService.selectById(userId);
        AssertUtils.notNull(remoteUserVo, "用户信息不存在");
        retUser.setNickName(remoteUserVo.getNickName());
        retUser.setAvatar(remoteUserVo.getAvatar());
        retUser.setSex(remoteUserVo.getSex());

        List<UserAlbumVo> albums = userAlbumService.listVoByUserId(userId);
        retUser.setAlbums(albums);

        List<UserTagVo> tags = userTagService.listVoByUserId(userId);
        retUser.setTags(tags);

        List<UserRequireTagVo> requireTags = userRequireTagService.listVoByUserId(userId);
        retUser.setRequireTags(requireTags);
        return retUser;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDetail(AppUserDetailBo bo) {
        UserDetail detail = MapstructUtils.convert(bo, UserDetail.class);
        // 更新相册
        userAlbumService.update(detail.getUserId(), bo.getAlbumFileIds());

        // 更新用户标签
        userTagService.update(detail.getUserId(), bo.getTags());

        // 更新用户要求标签
        userRequireTagService.update(detail.getUserId(), bo.getRequireTags());

        return updateById(detail);
    }

    @Override
    public AppUserMyProfileVo getMyProfileByUserId(Long userId) {
        UserDetail userDetail = existById(userId);
        AppUserMyProfileVo ret = MapstructUtils.convert(userDetail, AppUserMyProfileVo.class);

        AppUserMyProfileStatCountVo count = baseMapper.myStat(userId);
        ret.setCount(count);
        return ret;
    }

    @Override
    public void validExistByUserId(Long userId) {
        validateExists(userId, "拉黑用户信息不存在！");
    }

    @Override
    public Map<Long, String> queryUserTitleMapByUserIds(List<Long> oppositeUserIds) {
        List<UserDetail> userDetails = listByIds(oppositeUserIds);
        return StreamUtils.toMap(userDetails, UserDetail::getUserId, user -> {
            String ageStr = UserUtils.getAgeStr(user.getBirthday());
            String userJobVal = remoteDictService.selectLabelByType(UserJob.getDictType(), user.getJob());
            String userRevenueVal = remoteDictService.selectLabelByType(UserRevenue.getDictType(), user.getRevenue());
            return String.join(",", userJobVal, userRevenueVal, ageStr);
        });
    }

    @Override
    public UserDetail getByUserId(Long userId) {
        return baseMapper.selectById(userId);
    }

    @Override
    public void updateVip(Long userId, LocalDate startDate, LocalDate endDate) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setVipStartDate(startDate);
        userDetail.setVipEndDate(endDate);
        userDetail.setIsVip(Boolean.TRUE);
        updateById(userDetail);
    }

    @Override
    public void updateAuthIdentityStatus(Long userId, Boolean isIdentity) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setIsIdentity(isIdentity);
        updateById(userDetail);
    }

    @Override
    public UserDetail existById(Long userId) {
        return existById(userId, "用户信息不存在！");
    }

    /**
     * 创建用户pid
     *
     * @return pid
     */
    @Lock4j
    public int createPid() {
        String pidKey = String.format(USER_PID_PREFIX, TenantHelper.getTenantId());
        long pid = RedisUtils.getAtomicValue(pidKey);
        if (pid == 0) {
            UserDetail userDetail = baseMapper.selectOne(Wrappers.<UserDetail>lambdaQuery()
                .select(UserDetail::getPid).orderByDesc(UserDetail::getPid).last("limit 1"));
            pid = null == userDetail ? 756895L : userDetail.getPid();
            RedisUtils.setAtomicValue(pidKey, pid);
        }
        return Long.valueOf(RedisUtils.incrAtomicValue(pidKey)).intValue();
    }
}
