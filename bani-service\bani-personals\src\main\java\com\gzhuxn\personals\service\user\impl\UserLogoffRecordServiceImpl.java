package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.exception.ServiceException;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.constant.PersonalsConstant;
import com.gzhuxn.personals.domain.user.UserLogoffRecord;
import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;
import com.gzhuxn.personals.enums.user.UserLogoffStatus;
import com.gzhuxn.personals.event.user.UserLogoffProducer;
import com.gzhuxn.personals.mapper.user.UserLogoffRecordMapper;
import com.gzhuxn.personals.service.user.IUserDataCleanupService;
import com.gzhuxn.personals.service.user.IUserLogoffRecordService;
import com.gzhuxn.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 用户-用户注销记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserLogoffRecordServiceImpl
    extends BaniServiceImpl<UserLogoffRecordMapper, UserLogoffRecord> implements IUserLogoffRecordService {
    private final UserLogoffProducer userLogoffProducer;
    private final IUserDataCleanupService userDataCleanupService;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询用户-用户注销记录
     *
     * @param id 主键
     * @return 用户-用户注销记录
     */
    @Override
    public UserLogoffRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-用户注销记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-用户注销记录分页列表
     */
    @Override
    public TableDataInfo<UserLogoffRecordVo> queryPageList(UserLogoffRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserLogoffRecord> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserLogoffRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-用户注销记录列表
     *
     * @param bo 查询条件
     * @return 用户-用户注销记录列表
     */
    @Override
    public List<UserLogoffRecordVo> queryList(UserLogoffRecordBo bo) {
        LambdaQueryWrapper<UserLogoffRecord> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 校验并批量删除用户-用户注销记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyUserLogoff() {
        Long userId = LoginHelper.getUserId();

        // 检查用户是否已经注销
        Boolean isLoggedOff = remoteUserService.isUserLoggedOff(userId);
        if (Boolean.TRUE.equals(isLoggedOff)) {
            throw new ServiceException("用户已注销，无法申请注销");
        }

        // 检查是否已有进行中的注销申请
        LambdaQueryWrapper<UserLogoffRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLogoffRecord::getUserId, userId)
            .eq(UserLogoffRecord::getStatus, UserLogoffStatus.PROCESSING.getValue());
        UserLogoffRecord existingRecord = baseMapper.selectOne(queryWrapper);

        if (existingRecord != null) {
            throw new ServiceException("您已有进行中的注销申请，请勿重复申请");
        }

        // 创建注销记录
        UserLogoffRecord logoffRecord = new UserLogoffRecord();
        logoffRecord.setUserId(userId);
        logoffRecord.setStatus(UserLogoffStatus.PROCESSING.getValue());
        logoffRecord.setLogoffTime(LocalDateTime.now().plusSeconds(PersonalsConstant.USER_LOGOFF_DELAY_TIME));

        boolean result = baseMapper.insert(logoffRecord) > 0;

        if (result) {
            // 发送延时事件
            userLogoffProducer.sendLogoffEvent(logoffRecord.getId(), userId);
            log.info("用户注销申请成功，用户ID={}，注销记录ID={}，预计注销时间={}",
                userId, logoffRecord.getId(), logoffRecord.getLogoffTime());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelUserLogoff(Long id) {
        Long userId = LoginHelper.getUserId();

        // 查询注销记录
        UserLogoffRecord logoffRecord = baseMapper.selectById(id);
        if (logoffRecord == null) {
            throw new ServiceException("注销记录不存在");
        }

        if (!logoffRecord.getUserId().equals(userId)) {
            throw new ServiceException("无权限操作此注销记录");
        }

        if (!UserLogoffStatus.PROCESSING.getValue().equals(logoffRecord.getStatus())) {
            throw new ServiceException("只能取消进行中的注销申请");
        }

        // 删除注销记录
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processUserLogoff(Long logoffRecordId, Long userId) {
        log.info("开始处理用户注销，注销记录ID={}，用户ID={}", logoffRecordId, userId);

        // 查询注销记录
        UserLogoffRecord logoffRecord = baseMapper.selectById(logoffRecordId);
        AssertUtils.notNull(logoffRecord, "注销记录不存在");

        if (!UserLogoffStatus.PROCESSING.getValue().equals(logoffRecord.getStatus())) {
            throw new ServiceException("注销记录状态异常");
        }

        try {
            // 1. 标记用户为注销状态（软删除）
            log.info("标记用户为注销状态，用户ID={}", userId);
            markUserAsLoggedOff(userId);

            // 2. 清理用户个人数据
            log.info("开始清理用户个人数据，用户ID={}", userId);
            userDataCleanupService.cleanupAllUserData(userId);

            // 3. 更新注销记录状态
            logoffRecord.setStatus(UserLogoffStatus.SUCCESS.getValue());
            logoffRecord.setSucceedTime(LocalDateTime.now());
            baseMapper.updateById(logoffRecord);

            log.info("用户注销处理完成，用户ID={}", userId);

        } catch (Exception e) {
            log.error("用户注销处理失败，注销记录ID={}，用户ID={}，错误信息：{}",
                logoffRecordId, userId, e.getMessage(), e);
            throw new ServiceException("用户注销处理失败：" + e.getMessage());
        }
    }

    /**
     * 标记用户为注销状态
     *
     * @param userId 用户ID
     */
    private void markUserAsLoggedOff(Long userId) {
        try {
            // 调用远程用户服务进行软删除
            Boolean result = remoteUserService.softDeleteUser(userId);
            if (Boolean.FALSE.equals(result)) {
                throw new ServiceException("标记用户注销状态失败");
            }

            log.info("用户注销状态标记成功，用户ID={}", userId);
        } catch (Exception e) {
            log.error("标记用户注销状态失败，用户ID={}，错误信息：{}", userId, e.getMessage(), e);
            throw new ServiceException("标记用户注销状态失败：" + e.getMessage());
        }
    }
}
