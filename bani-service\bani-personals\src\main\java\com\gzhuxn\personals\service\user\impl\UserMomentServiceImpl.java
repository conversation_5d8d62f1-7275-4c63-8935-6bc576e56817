package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.domain.user.UserMoment;
import com.gzhuxn.personals.domain.user.bo.UserMomentBo;
import com.gzhuxn.personals.domain.user.vo.UserMomentVo;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.user.moment.MomentStatus;
import com.gzhuxn.personals.mapper.user.UserMomentMapper;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.user.IUserMomentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 用户-动态Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class UserMomentServiceImpl
    extends BaniServiceImpl<UserMomentMapper, UserMoment> implements IUserMomentService {

    private final IContentAuditRecordService contentAuditRecordService;

    /**
     * 查询用户-动态
     *
     * @param id 主键
     * @return 用户-动态
     */
    @Override
    public UserMomentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-动态列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-动态分页列表
     */
    @Override
    public TableDataInfo<UserMomentVo> queryPageList(UserMomentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserMoment> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserMomentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-动态列表
     *
     * @param bo 查询条件
     * @return 用户-动态列表
     */
    @Override
    public List<UserMomentVo> queryList(UserMomentBo bo) {
        LambdaQueryWrapper<UserMoment> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增用户-动态
     *
     * @param bo 用户-动态
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(UserMomentBo bo) {
        UserMoment add = MapstructUtils.convert(bo, UserMoment.class);

        // 设置当前用户ID
        add.setCreateBy(LoginHelper.getUserId());
        add.setUserId(LoginHelper.getUserId());

        // 如果是发布状态，设置为待审核状态
        if (MomentStatus.PUBLISHED.getCode().equals(bo.getStatus())) {
            add.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        } else {
            // 草稿状态，设置为草稿审核状态
            add.setAuditStatus(AuditStatus.DRAFT.getValue());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            // 如果是发布状态，发起异步审核
            if (MomentStatus.PUBLISHED.getCode().equals(bo.getStatus())) {
                contentAuditRecordService.createAudit(add.getId(), AuditType.MOMENT);
            }
        }
        return flag;
    }

    /**
     * 修改用户-动态
     *
     * @param bo 用户-动态
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(UserMomentBo bo) {
        UserMoment update = MapstructUtils.convert(bo, UserMoment.class);

        // 查询原有动态
        UserMoment existing = baseMapper.selectById(bo.getId());
        AssertUtils.notNull(existing, "动态不存在");

        // 检查权限：只能修改自己的动态
        AssertUtils.isTrue(existing.getCreateBy().equals(LoginHelper.getUserId()), "无权限修改此动态");

        // 如果从草稿改为发布状态，需要重新审核
        if (MomentStatus.DRAFT.getCode().equals(existing.getStatus()) &&
            MomentStatus.PUBLISHED.getCode().equals(bo.getStatus())) {
            update.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        }

        boolean flag = baseMapper.updateById(update) > 0;

        // 如果从草稿改为发布状态，发起异步审核
        if (flag && MomentStatus.DRAFT.getCode().equals(existing.getStatus()) &&
            MomentStatus.PUBLISHED.getCode().equals(bo.getStatus())) {
            contentAuditRecordService.createAudit(bo.getId(), AuditType.MOMENT);
        }

        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWithValidByIds(Collection<Long> ids, boolean isValid) {
        if (isValid) {
            ids = baseMapper.listIdsByIdsAndCreateUserId(ids, LoginHelper.getUserId());
            AssertUtils.notEmpty(ids, "未找到关联动态");
        }
        return removeBatchByIds(ids);
    }

    @Override
    public UserMoment getById(Long id) {
        return existById(id, "动态不存在");
    }

    @Override
    public void pass(Long id) {
        UserMoment moment = getById(id);
        moment.setAuditStatus(AuditStatus.PASS.getValue());
        moment.setStatus(MomentStatus.PUBLISHED.getCode());
        baseMapper.updateById(moment);
    }

    @Override
    public void reject(Long id, String reason) {
        UserMoment moment = getById(id);
        moment.setAuditStatus(AuditStatus.REJECT.getValue());
        // 回到草稿状态
        moment.setStatus(MomentStatus.DRAFT.getCode());
        baseMapper.updateById(moment);
    }

    @Override
    public void transferUser(Long businessId) {
        UserMoment moment = getById(businessId);
        moment.setAuditStatus(AuditStatus.TRANSFER_WAIT_AUDIT.getValue());
        baseMapper.updateById(moment);
    }
}
