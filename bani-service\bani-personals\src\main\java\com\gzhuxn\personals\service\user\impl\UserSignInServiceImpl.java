package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.UserSignIn;
import com.gzhuxn.personals.domain.user.bo.UserSignInBo;
import com.gzhuxn.personals.domain.user.vo.UserSignInVo;
import com.gzhuxn.personals.mapper.user.UserSignInMapper;
import com.gzhuxn.personals.service.user.IUserSignInService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 用户-签到Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@RequiredArgsConstructor
@Service
public class UserSignInServiceImpl implements IUserSignInService {

    private final UserSignInMapper baseMapper;

    /**
     * 查询用户-签到
     *
     * @param id 主键
     * @return 用户-签到
     */
    @Override
    public UserSignInVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-签到列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-签到分页列表
     */
    @Override
    public TableDataInfo<UserSignInVo> queryPageList(UserSignInBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserSignIn> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserSignInVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-签到列表
     *
     * @param bo 查询条件
     * @return 用户-签到列表
     */
    @Override
    public List<UserSignInVo> queryList(UserSignInBo bo) {
        LambdaQueryWrapper<UserSignIn> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增用户-签到
     *
     * @param bo 用户-签到
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(UserSignInBo bo) {
        UserSignIn add = MapstructUtils.convert(bo, UserSignIn.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户-签到
     *
     * @param bo 用户-签到
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(UserSignInBo bo) {
        UserSignIn update = MapstructUtils.convert(bo, UserSignIn.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserSignIn entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户-签到信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
