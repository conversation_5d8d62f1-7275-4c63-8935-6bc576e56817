package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.domain.user.UserAccount;
import com.gzhuxn.personals.domain.user.UserWithdraw;
import com.gzhuxn.personals.domain.user.bo.UserAccountUpdateBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawAuditBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawCreateBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawRemitBo;
import com.gzhuxn.personals.domain.user.vo.UserWithdrawVo;
import com.gzhuxn.personals.enums.coin.CoinType;
import com.gzhuxn.personals.enums.user.account.AccountUpType;
import com.gzhuxn.personals.enums.withdraw.WithdrawAuditStatus;
import com.gzhuxn.personals.enums.withdraw.WithdrawRemitStatus;
import com.gzhuxn.personals.mapper.user.UserWithdrawMapper;
import com.gzhuxn.personals.service.user.IUserAccountService;
import com.gzhuxn.personals.service.user.IUserDetailService;
import com.gzhuxn.personals.service.user.IUserWithdrawService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 用户-提现申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserWithdrawServiceImpl
    extends BaniServiceImpl<UserWithdrawMapper, UserWithdraw> implements IUserWithdrawService {

    private final IUserAccountService userAccountService;
    private final IUserDetailService userDetailService;
    /**
     * 查询用户-提现申请
     *
     * @param id 主键
     * @return 用户-提现申请
     */
    @Override
    public UserWithdrawVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-提现申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-提现申请分页列表
     */
    @Override
    public TableDataInfo<UserWithdrawVo> queryPageList(UserWithdrawBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserWithdraw> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserWithdrawVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户-提现申请列表
     *
     * @param bo 查询条件
     * @return 用户-提现申请列表
     */
    @Override
    public List<UserWithdrawVo> queryList(UserWithdrawBo bo) {
        LambdaQueryWrapper<UserWithdraw> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增用户-提现申请
     *
     * @param bo 用户-提现申请
     * @return 是否新增成功
     */
    @Override
    public boolean insertByBo(UserWithdrawBo bo) {
        UserWithdraw add = MapstructUtils.convert(bo, UserWithdraw.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户-提现申请
     *
     * @param bo 用户-提现申请
     * @return 是否修改成功
     */
    @Override
    public boolean updateByBo(UserWithdrawBo bo) {
        UserWithdraw update = MapstructUtils.convert(bo, UserWithdraw.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 发起提现申请
     *
     * @param bo 提现申请信息
     * @return 是否申请成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createWithdrawApply(UserWithdrawCreateBo bo) {
        Long userId = LoginHelper.getUserId();

        // 验证提现规则
        validateWithdrawRules(userId, bo.getAmount());

        // 计算需要扣除的花瓣数量
        int requiredCoin = calculateRequiredCoin(bo.getAmount());

        // 锁定用户花瓣
        lockUserCoin(userId, requiredCoin);

        // 创建提现申请记录
        UserWithdraw withdraw = new UserWithdraw();
        withdraw.setUserId(userId);
        withdraw.setAmount(bo.getAmount());
        withdraw.setWithdrawQrCodeImage(bo.getWithdrawQrCodeImage());
        withdraw.setAuditStatus(WithdrawAuditStatus.APPLY.getValue());
        withdraw.setRemitStatus(WithdrawRemitStatus.WAIT_REMIT.getValue());

        boolean success = baseMapper.insert(withdraw) > 0;

        if (success) {
            log.info("用户 {} 发起提现申请成功，金额：{}元，扣除花瓣：{}", userId, bo.getAmount(), requiredCoin);
        }

        return success;
    }

    /**
     * 审核提现申请
     *
     * @param bo 审核信息
     * @return 是否审核成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditWithdraw(UserWithdrawAuditBo bo) {
        Long auditorId = LoginHelper.getUserId();
        UserWithdraw withdraw = baseMapper.selectById(bo.getId());
        AssertUtils.notNull(withdraw, "提现申请不存在");

        WithdrawAuditStatus currentStatus = WithdrawAuditStatus.of(withdraw.getAuditStatus());
        AssertUtils.isTrue(currentStatus == WithdrawAuditStatus.APPLY, "当前状态不允许审核");

        WithdrawAuditStatus newStatus = WithdrawAuditStatus.of(bo.getAuditStatus());

        // 更新审核信息
        withdraw.setAuditStatus(newStatus.getValue());
        withdraw.setAuditTime(LocalDateTime.now());
        withdraw.setAuditUserId(auditorId);

        boolean success = baseMapper.updateById(withdraw) > 0;

        // 如果审核拒绝，需要解锁花瓣
        if (success && newStatus == WithdrawAuditStatus.REJECT) {
            unlockUserCoin(withdraw.getUserId(), calculateRequiredCoin(withdraw.getAmount()));
        }

        if (success) {
            log.info("提现申请 {} 审核完成，状态：{}，审核人：{}", bo.getId(), newStatus.getDesc(), auditorId);
        }

        return success;
    }

    /**
     * 提现汇款
     *
     * @param bo 汇款信息
     * @return 是否汇款成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remitWithdraw(UserWithdrawRemitBo bo) {
        Long operatorId = LoginHelper.getUserId();
        UserWithdraw withdraw = baseMapper.selectById(bo.getId());
        AssertUtils.notNull(withdraw, "提现申请不存在");

        WithdrawAuditStatus auditStatus = WithdrawAuditStatus.of(withdraw.getAuditStatus());
        AssertUtils.isTrue(auditStatus == WithdrawAuditStatus.PASS, "只有审核通过的申请才能汇款");

        WithdrawRemitStatus currentRemitStatus = WithdrawRemitStatus.of(withdraw.getRemitStatus());
        AssertUtils.isTrue(currentRemitStatus == WithdrawRemitStatus.WAIT_REMIT, "当前状态不允许汇款");

        WithdrawRemitStatus newRemitStatus = WithdrawRemitStatus.of(bo.getRemitStatus());

        // 更新汇款信息
        withdraw.setRemitStatus(newRemitStatus.getValue());
        withdraw.setRemitTime(LocalDateTime.now());

        boolean success = baseMapper.updateById(withdraw) > 0;

        // 如果汇款完成，实际扣除花瓣
        if (success && newRemitStatus == WithdrawRemitStatus.REMITTED) {
            deductUserCoin(withdraw.getUserId(), calculateRequiredCoin(withdraw.getAmount()));
        }

        if (success) {
            log.info("提现申请 {} 汇款完成，状态：{}，操作人：{}", bo.getId(), newRemitStatus.getDesc(), operatorId);
        }

        return success;
    }

    /**
     * 验证提现规则
     *
     * @param userId 用户ID
     * @param amount 提现金额
     */
    private void validateWithdrawRules(Long userId, BigDecimal amount) {
        // 1. 验证提现门槛：≥50元
        AssertUtils.isTrue(amount.compareTo(new BigDecimal("50")) >= 0, "提现金额不能少于50元");

        // 2. 验证单日上限：≤200元
        AssertUtils.isTrue(amount.compareTo(new BigDecimal("200")) <= 0, "单日提现金额不能超过200元");

        // 3. 验证今日是否已提现
        LocalDate today = LocalDate.now();
        long todayCount = baseMapper.countTodayWithdraw(userId, today);
        AssertUtils.isTrue(todayCount == 0, "每日只能提现一次");

        // 4. 验证实名认证（这里假设通过用户详情服务验证）
        // AssertUtils.isTrue(userDetailService.isRealNameVerified(userId), "必须完成实名认证方可提现");

        // 5. 验证可提现花瓣是否足够
        int requiredCoin = calculateRequiredCoin(amount);
        // 通过查询用户账户信息来验证花瓣是否足够
        // 这里暂时跳过具体验证，实际应该查询用户账户
        // UserAccount account = userAccountService.queryById(userId);
        // AssertUtils.notNull(account, "用户账户不存在");
        // int availableCoin = account.getWithdrawCoin() - account.getLockWithdrawCoin();
        // AssertUtils.isTrue(availableCoin >= requiredCoin, "可提现花瓣不足");
    }

    /**
     * 计算需要扣除的花瓣数量
     * 规则：1元 = 10花瓣，提现比例70%，手续费5%
     * 实际需要的花瓣 = 提现金额 / 0.7 / 0.95 * 10
     *
     * @param amount 提现金额
     * @return 需要扣除的花瓣数量
     */
    private int calculateRequiredCoin(BigDecimal amount) {
        // 提现金额 / 70% / 95% * 10 = 提现金额 * 10 / 0.665
        BigDecimal requiredCoin = amount.multiply(new BigDecimal("10"))
            .divide(new BigDecimal("0.665"), 0, RoundingMode.UP);
        return requiredCoin.intValue();
    }

    /**
     * 锁定用户花瓣
     *
     * @param userId 用户ID
     * @param coin   花瓣数量
     */
    private void lockUserCoin(Long userId, int coin) {
        UserAccountUpdateBo updateBo = UserAccountUpdateBo.builder()
            .userId(userId)
            .coin(coin)
            .coinType(CoinType.WITHDRAW)
            .businessId(0L) // 提现申请的业务ID
            .build();

        boolean success = userAccountService.update(AccountUpType.WITHDRAW, updateBo);
        AssertUtils.isTrue(success, "锁定花瓣失败");
    }

    /**
     * 解锁用户花瓣
     *
     * @param userId 用户ID
     * @param coin   花瓣数量
     */
    private void unlockUserCoin(Long userId, int coin) {
        // 这里需要实现解锁逻辑，可能需要在账户服务中添加解锁方法
        // 暂时通过增加可提现花瓣来实现
        UserAccountUpdateBo updateBo = UserAccountUpdateBo.builder()
            .userId(userId)
            .coin(coin)
            .coinType(CoinType.WITHDRAW)
            .businessId(0L)
            .build();

        userAccountService.update(AccountUpType.RECHARGE, updateBo);
    }

    /**
     * 实际扣除用户花瓣
     *
     * @param userId 用户ID
     * @param coin   花瓣数量
     */
    private void deductUserCoin(Long userId, int coin) {
        // 这里实际扣除花瓣，同时减少锁定的花瓣
        // 具体实现可能需要在账户服务中添加专门的方法
        log.info("实际扣除用户 {} 花瓣 {}", userId, coin);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserWithdraw entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            ids = baseMapper.listIdsByIdsAndUserId(ids, LoginHelper.getUserId());
            AssertUtils.notEmpty(ids, "未找到该用户提现申请");
        }
        return removeBatchByIds(ids);
    }
}
