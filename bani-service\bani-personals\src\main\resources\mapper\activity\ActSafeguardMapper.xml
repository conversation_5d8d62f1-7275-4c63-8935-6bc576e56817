<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzhuxn.personals.mapper.activity.ActSafeguardMapper">

    <select id="queryAdminPageList"
            resultType="com.gzhuxn.personals.controller.admin.activity.vo.AdminActivitySafeguardPageVo">
        SELECT
        id,
        name,
        amount,
        safe_key,
        status,
        create_time,
        update_time,
        (SELECT
        GROUP_CONCAT(CONCAT(asi.name,'：',asi.des,'；'))
        FROM act_safeguard_item asi
        WHERE asi.del_flag = 0
        AND asi.safeguard_id = asf.id
        ) itemNames
        FROM act_safeguard asf
        WHERE del_flag = 0
        <if test="bo.searchValue != null and bo.searchValue != ''">
            AND name LIKE CONCAT('%', #{bo.searchValue}, '%')
        </if>
        <if test="bo.name != null and bo.name != ''">
            AND name LIKE CONCAT('%', #{bo.name}, '%')
        </if>
        <if test="bo.safeKey != null and bo.safeKey != ''">
            AND safe_key LIKE CONCAT('%', #{bo.safeKey}, '%')
        </if>
        <if test="bo.status != null">
            AND status #{bo.status}
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>
