<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzhuxn.personals.mapper.user.UserAuthPayRecordMapper">

    <resultMap type="com.gzhuxn.personals.domain.user.UserAuthPayRecord" id="UserAuthPayRecordResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="authApplyId" column="auth_apply_id"/>
        <result property="authType" column="auth_type"/>
        <result property="originalAmount" column="original_amount"/>
        <result property="amount" column="amount"/>
        <result property="coin" column="coin"/>
        <result property="payStatus" column="pay_status"/>
        <result property="orderId" column="order_id"/>
        <result property="useStatus" column="use_status"/>
        <result property="payTime" column="pay_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectUserAuthPayRecordVo">
        select id,
               user_id,
               auth_apply_id,
               auth_type,
               original_amount,
               amount,
               coin,
               pay_status,
               order_id,
               use_status,
               pay_time,
               create_time,
               update_time
        from user_auth_pay_record
    </sql>

    <select id="getByOrderId" parameterType="Long" resultMap="UserAuthPayRecordResult">
        <include refid="selectUserAuthPayRecordVo"/>
        where order_id = #{orderId}
    </select>

    <select id="listByIdsAndUserId" resultMap="UserAuthPayRecordResult">
        <include refid="selectUserAuthPayRecordVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
    </select>

    <select id="getUnusedPaidRecord" resultMap="UserAuthPayRecordResult">
        <include refid="selectUserAuthPayRecordVo"/>
        where user_id = #{userId}
        and auth_type = #{authType}
        and pay_status = 10
        and use_status = 0
        order by create_time desc
        limit 1
    </select>

    <update id="markAsUsed">
        update user_auth_pay_record
        set use_status  = 1,
            update_time = now()
        where id = #{id}
    </update>

</mapper>
