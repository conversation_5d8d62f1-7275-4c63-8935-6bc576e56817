package com.gzhuxn.personals;

import com.gzhuxn.personals.domain.user.bo.UserWithdrawCreateBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawAuditBo;
import com.gzhuxn.personals.domain.user.bo.UserWithdrawRemitBo;
import com.gzhuxn.personals.enums.withdraw.WithdrawAuditStatus;
import com.gzhuxn.personals.enums.withdraw.WithdrawRemitStatus;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户提现功能测试
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public class UserWithdrawTest {

    @Test
    public void testWithdrawCreateBo() {
        // 测试提现申请创建对象
        UserWithdrawCreateBo createBo = new UserWithdrawCreateBo();
        createBo.setAmount(new BigDecimal("100.00"));
        createBo.setWithdrawQrCodeImage(123L);

        // 验证属性设置
        assertEquals(new BigDecimal("100.00"), createBo.getAmount());
        assertEquals(Long.valueOf(123L), createBo.getWithdrawQrCodeImage());
    }

    @Test
    public void testWithdrawAuditBo() {
        // 测试提现审核对象
        UserWithdrawAuditBo auditBo = new UserWithdrawAuditBo();
        auditBo.setId(1L);
        auditBo.setAuditStatus(WithdrawAuditStatus.PASS.getValue());
        auditBo.setAuditRemark("审核通过");

        // 验证属性设置
        assertEquals(Long.valueOf(1L), auditBo.getId());
        assertEquals(WithdrawAuditStatus.PASS.getValue(), auditBo.getAuditStatus());
        assertEquals("审核通过", auditBo.getAuditRemark());
    }

    @Test
    public void testWithdrawRemitBo() {
        // 测试提现汇款对象
        UserWithdrawRemitBo remitBo = new UserWithdrawRemitBo();
        remitBo.setId(1L);
        remitBo.setRemitStatus(WithdrawRemitStatus.REMITTED.getValue());
        remitBo.setRemitRemark("汇款完成");

        // 验证属性设置
        assertEquals(Long.valueOf(1L), remitBo.getId());
        assertEquals(WithdrawRemitStatus.REMITTED.getValue(), remitBo.getRemitStatus());
        assertEquals("汇款完成", remitBo.getRemitRemark());
    }

    @Test
    public void testWithdrawAuditStatus() {
        // 测试提现审核状态枚举
        WithdrawAuditStatus apply = WithdrawAuditStatus.APPLY;
        WithdrawAuditStatus pass = WithdrawAuditStatus.PASS;
        WithdrawAuditStatus reject = WithdrawAuditStatus.REJECT;

        // 验证枚举值
        assertEquals(1, apply.getValue());
        assertEquals("发起申请", apply.getDesc());
        assertTrue(apply.canModify());
        assertFalse(apply.isCompleted());

        assertEquals(2, pass.getValue());
        assertEquals("审核通过", pass.getDesc());
        assertFalse(pass.canModify());
        assertTrue(pass.isCompleted());

        assertEquals(4, reject.getValue());
        assertEquals("拒绝", reject.getDesc());
        assertFalse(reject.canModify());
        assertTrue(reject.isCompleted());
    }

    @Test
    public void testWithdrawRemitStatus() {
        // 测试提现汇款状态枚举
        WithdrawRemitStatus waitRemit = WithdrawRemitStatus.WAIT_REMIT;
        WithdrawRemitStatus remitted = WithdrawRemitStatus.REMITTED;

        // 验证枚举值
        assertEquals(1, waitRemit.getValue());
        assertEquals("待汇款", waitRemit.getDesc());
        assertFalse(waitRemit.isRemitted());

        assertEquals(2, remitted.getValue());
        assertEquals("已汇款", remitted.getDesc());
        assertTrue(remitted.isRemitted());
    }

    @Test
    public void testWithdrawAmountValidation() {
        // 测试提现金额验证
        BigDecimal minAmount = new BigDecimal("50.00");
        BigDecimal maxAmount = new BigDecimal("200.00");
        BigDecimal validAmount = new BigDecimal("100.00");
        BigDecimal invalidLowAmount = new BigDecimal("30.00");
        BigDecimal invalidHighAmount = new BigDecimal("300.00");

        // 验证金额范围
        assertTrue(validAmount.compareTo(minAmount) >= 0);
        assertTrue(validAmount.compareTo(maxAmount) <= 0);
        
        assertFalse(invalidLowAmount.compareTo(minAmount) >= 0);
        assertFalse(invalidHighAmount.compareTo(maxAmount) <= 0);
    }

    @Test
    public void testCoinCalculation() {
        // 测试花瓣计算逻辑
        // 规则：1元 = 10花瓣，提现比例70%，手续费5%
        // 实际需要的花瓣 = 提现金额 / 0.7 / 0.95 * 10
        
        BigDecimal withdrawAmount = new BigDecimal("100.00");
        
        // 计算需要的花瓣数量
        BigDecimal requiredCoin = withdrawAmount.multiply(new BigDecimal("10"))
            .divide(new BigDecimal("0.665"), 0, RoundingMode.UP);
        
        // 验证计算结果
        assertTrue(requiredCoin.intValue() > 0);
        
        // 100元应该需要约1504花瓣 (100 * 10 / 0.665 ≈ 1504)
        assertEquals(1504, requiredCoin.intValue());
    }

    @Test
    public void testWithdrawableAmountCalculation() {
        // 测试可提现金额计算
        // 规则：1元 = 10花瓣，提现比例70%，手续费5%
        
        Integer withdrawCoin = 1000; // 1000花瓣
        
        if (withdrawCoin != null && withdrawCoin > 0) {
            // 1元 = 10花瓣，所以花瓣转换为元需要除以10
            BigDecimal coinToYuan = new BigDecimal(withdrawCoin).divide(new BigDecimal("10"), 2, RoundingMode.DOWN);
            
            // 提现比例70%
            BigDecimal withdrawableBase = coinToYuan.multiply(new BigDecimal("0.7"));
            
            // 扣除手续费5%，实际到账95%
            BigDecimal actualAmount = withdrawableBase.multiply(new BigDecimal("0.95"));
            
            // 验证计算结果
            assertEquals(new BigDecimal("100.00"), coinToYuan); // 1000花瓣 = 100元
            assertEquals(new BigDecimal("70.00"), withdrawableBase); // 70%可提现
            assertEquals(new BigDecimal("66.50"), actualAmount.setScale(2, RoundingMode.DOWN)); // 扣除手续费后
        }
    }

    @Test
    public void testWithdrawRules() {
        // 测试提现规则
        
        // 1. 提现门槛：≥50元
        BigDecimal minThreshold = new BigDecimal("50.00");
        assertTrue(new BigDecimal("100.00").compareTo(minThreshold) >= 0);
        assertFalse(new BigDecimal("30.00").compareTo(minThreshold) >= 0);
        
        // 2. 单日上限：≤200元
        BigDecimal maxDaily = new BigDecimal("200.00");
        assertTrue(new BigDecimal("100.00").compareTo(maxDaily) <= 0);
        assertFalse(new BigDecimal("300.00").compareTo(maxDaily) <= 0);
        
        // 3. 提现频率：每日≤1次
        int dailyLimit = 1;
        assertTrue(0 < dailyLimit); // 今日未提现
        assertFalse(1 < dailyLimit); // 今日已提现1次
    }

    @Test
    public void testWithdrawStatusFlow() {
        // 测试提现状态流转
        
        // 初始状态：发起申请
        WithdrawAuditStatus initialStatus = WithdrawAuditStatus.APPLY;
        assertTrue(initialStatus.canModify());
        assertFalse(initialStatus.isCompleted());
        
        // 审核通过
        WithdrawAuditStatus passStatus = WithdrawAuditStatus.PASS;
        assertFalse(passStatus.canModify());
        assertTrue(passStatus.isCompleted());
        
        // 审核拒绝
        WithdrawAuditStatus rejectStatus = WithdrawAuditStatus.REJECT;
        assertFalse(rejectStatus.canModify());
        assertTrue(rejectStatus.isCompleted());
        
        // 汇款状态
        WithdrawRemitStatus waitRemit = WithdrawRemitStatus.WAIT_REMIT;
        WithdrawRemitStatus remitted = WithdrawRemitStatus.REMITTED;
        
        assertFalse(waitRemit.isRemitted());
        assertTrue(remitted.isRemitted());
    }

    @Test
    public void testWithdrawBusinessLogic() {
        // 测试提现业务逻辑
        
        // 模拟用户账户信息
        Integer totalCoin = 2000; // 总花瓣
        Integer lockedCoin = 500;  // 锁定花瓣
        Integer availableCoin = totalCoin - lockedCoin; // 可用花瓣
        
        // 提现申请
        BigDecimal withdrawAmount = new BigDecimal("100.00");
        int requiredCoin = 1504; // 需要的花瓣数量
        
        // 验证是否有足够花瓣
        assertTrue(availableCoin >= requiredCoin, "可用花瓣不足");
        
        // 模拟锁定花瓣
        Integer newLockedCoin = lockedCoin + requiredCoin;
        Integer newAvailableCoin = totalCoin - newLockedCoin;
        
        assertEquals(Integer.valueOf(2004), newLockedCoin);
        assertEquals(Integer.valueOf(-4), newAvailableCoin); // 这种情况下花瓣不足
    }

    @Test
    public void testEnumValueOf() {
        // 测试枚举值转换
        
        // 审核状态
        WithdrawAuditStatus status1 = WithdrawAuditStatus.of(1);
        assertEquals(WithdrawAuditStatus.APPLY, status1);
        
        WithdrawAuditStatus status2 = WithdrawAuditStatus.of(2);
        assertEquals(WithdrawAuditStatus.PASS, status2);
        
        // 汇款状态
        WithdrawRemitStatus remitStatus1 = WithdrawRemitStatus.of(1);
        assertEquals(WithdrawRemitStatus.WAIT_REMIT, remitStatus1);
        
        WithdrawRemitStatus remitStatus2 = WithdrawRemitStatus.of(2);
        assertEquals(WithdrawRemitStatus.REMITTED, remitStatus2);
    }

    @Test
    public void testWithdrawFeeCalculation() {
        // 测试提现手续费计算
        
        BigDecimal withdrawAmount = new BigDecimal("100.00");
        BigDecimal feeRate = new BigDecimal("0.05"); // 5%手续费
        
        // 计算手续费
        BigDecimal fee = withdrawAmount.multiply(feeRate);
        BigDecimal actualAmount = withdrawAmount.subtract(fee);
        
        assertEquals(new BigDecimal("5.00"), fee);
        assertEquals(new BigDecimal("95.00"), actualAmount);
        
        // 验证手续费比例
        BigDecimal feePercentage = fee.divide(withdrawAmount, 4, RoundingMode.HALF_UP);
        assertEquals(new BigDecimal("0.0500"), feePercentage);
    }
}
