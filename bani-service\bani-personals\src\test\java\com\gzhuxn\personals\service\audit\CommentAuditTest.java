package com.gzhuxn.personals.service.audit;

import com.gzhuxn.personals.domain.user.UserComment;
import com.gzhuxn.personals.domain.user.bo.UserCommentBo;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.audit.processor.AuditCommentProcessor;
import com.gzhuxn.personals.service.user.impl.UserCommentServiceImpl;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.domain.RemoteCheckText;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 评论审核测试
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@SpringBootTest
@ActiveProfiles("test")
public class CommentAuditTest {

    @Mock
    private UserCommentServiceImpl userCommentService;

    @Mock
    private RemoteContentAuditService remoteContentAuditService;

    @Mock
    private IContentAuditRecordService contentAuditRecordService;

    @Test
    public void testCreateCommentWithAudit() {
        // 测试创建评论并发起审核
        UserCommentBo commentBo = new UserCommentBo();
        commentBo.setContent("这是一条测试评论");
        commentBo.setType(1); // 动态评论
        commentBo.setBusinessId(123L);
        commentBo.setParentId(0L);

        // 模拟创建成功
        when(userCommentService.insertByBo(commentBo)).thenReturn(true);
        when(contentAuditRecordService.createAudit(any(), eq(AuditType.COMMENT))).thenReturn(true);

        // 执行创建
        boolean result = userCommentService.insertByBo(commentBo);

        // 验证结果
        assertTrue(result);
        verify(contentAuditRecordService, times(1)).createAudit(any(), eq(AuditType.COMMENT));
    }

    @Test
    public void testCommentAuditProcessor() {
        // 测试评论审核处理器
        AuditCommentProcessor processor = new AuditCommentProcessor();
        
        // 验证审核类型
        assertEquals(AuditType.COMMENT, processor.getAuditType());
    }

    @Test
    public void testCommentAuditPass() {
        // 测试评论审核通过
        UserComment comment = new UserComment();
        comment.setId(1L);
        comment.setContent("正常的评论内容");
        comment.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());

        // 模拟审核通过
        RemoteCheckText checkText = new RemoteCheckText();
        when(remoteContentAuditService.checkText(comment.getContent())).thenReturn(checkText);
        when(checkText.isPass()).thenReturn(true);

        // 验证审核通过后的状态
        assertEquals(AuditStatus.WAIT_AUDIT.getValue(), comment.getAuditStatus());
    }

    @Test
    public void testCommentAuditReject() {
        // 测试评论审核拒绝
        UserComment comment = new UserComment();
        comment.setId(1L);
        comment.setContent("违规评论内容");
        comment.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());

        // 模拟审核不通过
        RemoteCheckText checkText = new RemoteCheckText();
        when(remoteContentAuditService.checkText(comment.getContent())).thenReturn(checkText);
        when(checkText.isPass()).thenReturn(false);
        when(checkText.failMag()).thenReturn("评论包含违规信息");

        // 验证审核拒绝后的状态
        assertEquals(AuditStatus.WAIT_AUDIT.getValue(), comment.getAuditStatus());
    }

    @Test
    public void testUpdateCommentWithContentChange() {
        // 测试更新评论内容时重新审核
        UserCommentBo commentBo = new UserCommentBo();
        commentBo.setId(1L);
        commentBo.setContent("更新后的评论内容");

        UserComment existingComment = new UserComment();
        existingComment.setId(1L);
        existingComment.setContent("原始评论内容");
        existingComment.setCreateBy(1L);

        // 模拟查询和更新
        when(userCommentService.getById(1L)).thenReturn(existingComment);
        when(userCommentService.updateByBo(commentBo)).thenReturn(true);
        when(contentAuditRecordService.createAudit(1L, AuditType.COMMENT)).thenReturn(true);

        // 执行更新
        boolean result = userCommentService.updateByBo(commentBo);

        // 验证结果：内容变化应该发起审核
        assertTrue(result);
        verify(contentAuditRecordService, times(1)).createAudit(1L, AuditType.COMMENT);
    }

    @Test
    public void testUpdateCommentWithoutContentChange() {
        // 测试更新评论但内容未变化
        UserCommentBo commentBo = new UserCommentBo();
        commentBo.setId(1L);
        commentBo.setContent("相同的评论内容");

        UserComment existingComment = new UserComment();
        existingComment.setId(1L);
        existingComment.setContent("相同的评论内容");
        existingComment.setCreateBy(1L);

        // 模拟查询和更新
        when(userCommentService.getById(1L)).thenReturn(existingComment);
        when(userCommentService.updateByBo(commentBo)).thenReturn(true);

        // 执行更新
        boolean result = userCommentService.updateByBo(commentBo);

        // 验证结果：内容未变化不应该发起审核
        assertTrue(result);
        verify(contentAuditRecordService, never()).createAudit(any(), any());
    }

    @Test
    public void testDeleteCommentWithPermissionCheck() {
        // 测试删除评论时的权限检查
        Long[] ids = {1L, 2L, 3L};
        
        // 模拟删除成功
        when(userCommentService.deleteWithValidByIds(any(), eq(true))).thenReturn(true);

        // 执行删除
        boolean result = userCommentService.deleteWithValidByIds(List.of(ids), true);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testCommentAuditStatusTransition() {
        // 测试评论审核状态转换
        UserComment comment = new UserComment();
        comment.setId(1L);
        comment.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());

        // 测试审核通过
        userCommentService.publishComment(comment);
        verify(userCommentService, times(1)).publishComment(comment);

        // 测试审核拒绝
        userCommentService.rejectComment(comment);
        verify(userCommentService, times(1)).rejectComment(comment);

        // 测试转人工审核
        userCommentService.transferToManualAudit(comment);
        verify(userCommentService, times(1)).transferToManualAudit(comment);
    }

    @Test
    public void testCommentTypes() {
        // 测试评论类型
        UserCommentBo momentComment = new UserCommentBo();
        momentComment.setType(1); // 动态评论
        momentComment.setBusinessId(123L);
        momentComment.setContent("动态评论内容");

        UserCommentBo activityComment = new UserCommentBo();
        activityComment.setType(2); // 活动评论
        activityComment.setBusinessId(456L);
        activityComment.setContent("活动评论内容");

        // 验证评论类型设置
        assertEquals(1, momentComment.getType());
        assertEquals(2, activityComment.getType());
    }

    @Test
    public void testReplyComment() {
        // 测试回复评论
        UserCommentBo replyComment = new UserCommentBo();
        replyComment.setParentId(100L); // 父评论ID
        replyComment.setContent("这是一条回复");
        replyComment.setType(1);
        replyComment.setBusinessId(123L);

        // 模拟创建回复成功
        when(userCommentService.insertByBo(replyComment)).thenReturn(true);
        when(contentAuditRecordService.createAudit(any(), eq(AuditType.COMMENT))).thenReturn(true);

        // 执行创建回复
        boolean result = userCommentService.insertByBo(replyComment);

        // 验证结果：回复也需要审核
        assertTrue(result);
        verify(contentAuditRecordService, times(1)).createAudit(any(), eq(AuditType.COMMENT));
    }
}
