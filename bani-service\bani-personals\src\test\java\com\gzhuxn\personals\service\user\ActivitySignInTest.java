package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.base.enums.PayStatus;
import com.gzhuxn.personals.controller.app.user.bo.ActivitySignInBo;
import com.gzhuxn.personals.controller.app.user.vo.ActivitySignInResultVo;
import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.user.UserActivityRecord;
import com.gzhuxn.personals.enums.activity.ActivityStatus;
import com.gzhuxn.personals.service.activity.IActivityService;
import com.gzhuxn.personals.service.user.impl.UserActivityRecordServiceImpl;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 活动签到测试
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@SpringBootTest
@ActiveProfiles("test")
public class ActivitySignInTest {

    @Mock
    private UserActivityRecordServiceImpl userActivityRecordService;

    @Mock
    private IActivityService activityService;

    @Test
    public void testSuccessfulSignIn() {
        // 测试成功签到
        ActivitySignInBo signInBo = new ActivitySignInBo();
        signInBo.setActivityRecordId(1L);
        signInBo.setSignInAddr("北京市朝阳区");
        signInBo.setSignInLon(116.4074);
        signInBo.setSignInLat(39.9042);

        // 模拟活动记录
        UserActivityRecord record = new UserActivityRecord();
        record.setId(1L);
        record.setUserId(100L);
        record.setActivityId(200L);
        record.setPayStatus(PayStatus.SUCCESS.getValue());
        record.setSignIn(false);

        // 模拟活动信息
        Activity activity = new Activity();
        activity.setId(200L);
        activity.setName("测试活动");
        activity.setStatus(ActivityStatus.ACTIVITY_START.getValue());
        activity.setStartTime(LocalDateTime.now().minusHours(1));
        activity.setEndTime(LocalDateTime.now().plusHours(2));
        activity.setLon(116.4074);
        activity.setLat(39.9042);

        // 模拟签到结果
        ActivitySignInResultVo expectedResult = new ActivitySignInResultVo();
        expectedResult.setActivityRecordId(1L);
        expectedResult.setActivityId(200L);
        expectedResult.setActivityName("测试活动");
        expectedResult.setSignIn(true);
        expectedResult.setMessage("签到成功！");

        when(userActivityRecordService.signIn(signInBo)).thenReturn(expectedResult);

        // 执行签到
        ActivitySignInResultVo result = userActivityRecordService.signIn(signInBo);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getActivityRecordId());
        assertEquals(200L, result.getActivityId());
        assertEquals("测试活动", result.getActivityName());
        assertTrue(result.getSignIn());
        assertEquals("签到成功！", result.getMessage());
    }

    @Test
    public void testSignInValidation() {
        // 测试签到验证逻辑
        ActivitySignInBo signInBo = new ActivitySignInBo();
        signInBo.setActivityRecordId(1L);
        signInBo.setSignInAddr("北京市朝阳区");
        signInBo.setSignInLon(116.4074);
        signInBo.setSignInLat(39.9042);

        // 验证必填字段
        assertNotNull(signInBo.getActivityRecordId());
        assertNotNull(signInBo.getSignInAddr());
        assertNotNull(signInBo.getSignInLon());
        assertNotNull(signInBo.getSignInLat());
    }

    @Test
    public void testSignInLocationValidation() {
        // 测试位置验证
        double activityLat = 39.9042;
        double activityLon = 116.4074;

        // 测试在范围内的位置（100米内）
        double nearbyLat = 39.9043;
        double nearbyLon = 116.4075;

        // 测试超出范围的位置（1000米外）
        double farLat = 39.9142;
        double farLon = 116.4174;

        // 验证位置坐标格式
        assertTrue(activityLat >= -90 && activityLat <= 90);
        assertTrue(activityLon >= -180 && activityLon <= 180);
        assertTrue(nearbyLat >= -90 && nearbyLat <= 90);
        assertTrue(nearbyLon >= -180 && nearbyLon <= 180);
    }

    @Test
    public void testSignInTimeValidation() {
        // 测试签到时间验证
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime activityStart = now.minusHours(1);
        LocalDateTime activityEnd = now.plusHours(2);

        // 验证当前时间在活动时间范围内
        assertTrue(now.isAfter(activityStart));
        assertTrue(now.isBefore(activityEnd));

        // 测试活动未开始的情况
        LocalDateTime futureStart = now.plusHours(1);
        LocalDateTime futureEnd = now.plusHours(3);
        assertFalse(now.isAfter(futureStart));

        // 测试活动已结束的情况
        LocalDateTime pastStart = now.minusHours(3);
        LocalDateTime pastEnd = now.minusHours(1);
        assertFalse(now.isBefore(pastEnd));
    }

    @Test
    public void testPaymentStatusValidation() {
        // 测试支付状态验证
        UserActivityRecord record = new UserActivityRecord();

        // 测试已支付状态
        record.setPayStatus(PayStatus.SUCCESS.getValue());
        assertEquals(PayStatus.SUCCESS.getValue(), record.getPayStatus());

        // 测试未支付状态
        record.setPayStatus(PayStatus.WAIT_PAY.getValue());
        assertEquals(PayStatus.WAIT_PAY.getValue(), record.getPayStatus());

        // 测试支付失败状态
        record.setPayStatus(PayStatus.FAIL.getValue());
        assertEquals(PayStatus.FAIL.getValue(), record.getPayStatus());
    }

    @Test
    public void testActivityStatusValidation() {
        // 测试活动状态验证
        Activity activity = new Activity();

        // 测试活动进行中状态
        activity.setStatus(ActivityStatus.ACTIVITY_START.getValue());
        assertEquals(ActivityStatus.ACTIVITY_START.getValue(), activity.getStatus());

        // 测试活动报名中状态
        activity.setStatus(ActivityStatus.ENROLL_START.getValue());
        assertEquals(ActivityStatus.ENROLL_START.getValue(), activity.getStatus());

        // 测试活动已结束状态
        activity.setStatus(ActivityStatus.ACTIVITY_END.getValue());
        assertEquals(ActivityStatus.ACTIVITY_END.getValue(), activity.getStatus());
    }

    @Test
    public void testSignInResultVo() {
        // 测试签到结果对象
        ActivitySignInResultVo result = new ActivitySignInResultVo();
        result.setActivityRecordId(1L);
        result.setActivityId(200L);
        result.setActivityName("测试活动");
        result.setSignInTime(LocalDateTime.now());
        result.setSignInAddr("北京市朝阳区");
        result.setSignIn(true);
        result.setMessage("签到成功！");

        // 验证属性设置
        assertEquals(1L, result.getActivityRecordId());
        assertEquals(200L, result.getActivityId());
        assertEquals("测试活动", result.getActivityName());
        assertNotNull(result.getSignInTime());
        assertEquals("北京市朝阳区", result.getSignInAddr());
        assertTrue(result.getSignIn());
        assertEquals("签到成功！", result.getMessage());
    }

    @Test
    public void testDistanceCalculation() {
        // 测试距离计算（模拟）
        double lat1 = 39.9042; // 北京天安门
        double lon1 = 116.4074;
        double lat2 = 39.9043; // 附近位置
        double lon2 = 116.4075;

        // 验证坐标有效性
        assertTrue(lat1 >= -90 && lat1 <= 90);
        assertTrue(lon1 >= -180 && lon1 <= 180);
        assertTrue(lat2 >= -90 && lat2 <= 90);
        assertTrue(lon2 >= -180 && lon2 <= 180);

        // 验证坐标差值很小（表示距离很近）
        double latDiff = Math.abs(lat2 - lat1);
        double lonDiff = Math.abs(lon2 - lon1);
        assertTrue(latDiff < 0.01); // 纬度差小于0.01度
        assertTrue(lonDiff < 0.01); // 经度差小于0.01度
    }

    @Test
    public void testSignInBo() {
        // 测试签到业务对象
        ActivitySignInBo bo = new ActivitySignInBo();
        bo.setActivityRecordId(1L);
        bo.setSignInAddr("北京市朝阳区");
        bo.setSignInLon(116.4074);
        bo.setSignInLat(39.9042);

        // 验证属性设置
        assertEquals(1L, bo.getActivityRecordId());
        assertEquals("北京市朝阳区", bo.getSignInAddr());
        assertEquals(116.4074, bo.getSignInLon());
        assertEquals(39.9042, bo.getSignInLat());
    }
}
