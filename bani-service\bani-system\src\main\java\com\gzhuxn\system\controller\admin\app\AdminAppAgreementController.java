package com.gzhuxn.system.controller.admin.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppAgreementStatusUpBo;
import com.gzhuxn.system.controller.admin.app.vo.AdminAppAgreementPageVo;
import com.gzhuxn.system.domain.bo.AppAgreementBo;
import com.gzhuxn.system.domain.vo.AppAgreementVo;
import com.gzhuxn.system.service.IAppAgreementService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * app应用-协议配置
 * 前端访问路由地址为:/app/agreement
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/agreement")
public class AdminAppAgreementController extends BaseController {

    private final IAppAgreementService agreementService;

    /**
     * 查询基础-协议配置列表
     */
    @SaCheckPermission("system:app:agreement:page")
    @GetMapping("/page")
    public TableDataInfo<AdminAppAgreementPageVo> page(AppAgreementBo bo, PageQuery pageQuery) {
        return agreementService.queryAdminPageList(bo, pageQuery);
    }

    /**
     * 获取基础-协议配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:app:agreement:detail")
    @GetMapping("/detail")
    public R<AppAgreementVo> detail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(agreementService.queryAdminById(id));
    }

    /**
     * 应用协议-新增
     */
    @SaCheckPermission("system:app:agreement:create")
    @Log(title = "应用协议-新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody AppAgreementBo bo) {
        return toAjax(agreementService.insertAdminByBo(bo));
    }

    /**
     * 应用协议-修改
     */
    @SaCheckPermission("system:app:agreement:update")
    @Log(title = "应用协议-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody AppAgreementBo bo) {
        return toAjax(agreementService.updateAdminByBo(bo));
    }

    /**
     * 应用协议-修改状态
     */
    @SaCheckPermission("system:app:agreement:update")
    @Log(title = "应用协议-修改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated(EditGroup.class) @RequestBody AdminAppAgreementStatusUpBo bo) {
        return toAjax(agreementService.updateAdminStatus(bo));
    }

    /**
     * 应用协议-删除
     *
     * @param bo 主键串
     */
    @SaCheckPermission("system:app:agreement:remove")
    @Log(title = "应用协议-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> remove(@Valid DeleteBo bo) {
        return toAjax(agreementService.deleteWithValidByIds(bo.getIds(), true));
    }
}
