package com.gzhuxn.system.controller.admin.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.excel.utils.ExcelUtil;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.system.domain.bo.AppFeedbackBo;
import com.gzhuxn.system.domain.vo.AppFeedbackVo;
import com.gzhuxn.system.service.IAppFeedbackService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app应用-用户反馈意见
 * 前端访问路由地址为:/app/feedback
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/feedback")
public class AdminAppFeedbackController extends BaseController {

    private final IAppFeedbackService AppFeedbackService;

    /**
     * 查询基础-用户反馈意见列表
     */
    @SaCheckPermission("system:app:feedback:list")
    @GetMapping("/list")
    public TableDataInfo<AppFeedbackVo> list(AppFeedbackBo bo, PageQuery pageQuery) {
        return AppFeedbackService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出基础-用户反馈意见列表
     */
    @SaCheckPermission("system:app:feedback:export")
    @Log(title = "app用户反馈-导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppFeedbackBo bo, HttpServletResponse response) {
        List<AppFeedbackVo> list = AppFeedbackService.queryList(bo);
        ExcelUtil.exportExcel(list, "app用户反馈", AppFeedbackVo.class, response);
    }

    /**
     * 获取基础-用户反馈意见详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:app:feedback:query")
    @GetMapping("/{id}")
    public R<AppFeedbackVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(AppFeedbackService.queryById(id));
    }

    /**
     * 新增基础-用户反馈意见
     */
    @SaCheckPermission("system:app:feedback:add")
    @Log(title = "app用户反馈-新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody AppFeedbackBo bo) {
        return toAjax(AppFeedbackService.insertByBo(bo));
    }

    /**
     * 修改基础-用户反馈意见
     */
    @SaCheckPermission("system:app:feedback:edit")
    @Log(title = "app用户反馈-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody AppFeedbackBo bo) {
        return toAjax(AppFeedbackService.updateByBo(bo));
    }

    /**
     * 删除基础-用户反馈意见
     *
     * @param ids 主键串
     */


    @SaCheckPermission("system:app:feedback:remove")
    @Log(title = "app用户反馈-删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(AppFeedbackService.deleteWithValidByIds(List.of(ids), true));
    }
}
