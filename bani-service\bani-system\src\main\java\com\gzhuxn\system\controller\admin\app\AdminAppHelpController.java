package com.gzhuxn.system.controller.admin.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppHelpStatusUpBo;
import com.gzhuxn.system.domain.bo.AppHelpBo;
import com.gzhuxn.system.domain.vo.AppHelpVo;
import com.gzhuxn.system.service.IAppHelpService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * app应用-常见问题帮助
 * 前端访问路由地址为:/app/help
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/help")
public class AdminAppHelpController extends BaseController {

    private final IAppHelpService appHelpService;

    /**
     * 查询app应用-常见问题帮助列表
     */
    @SaCheckPermission("system:app:help:page")
    @GetMapping("/page")
    public TableDataInfo<AppHelpVo> page(AppHelpBo bo, PageQuery pageQuery) {
        return appHelpService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取app应用-常见问题帮助详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:app:help:detail")
    @GetMapping("/detail")
    public R<AppHelpVo> detail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(appHelpService.queryById(id));
    }

    /**
     * 应用帮助-新增
     */
    @SaCheckPermission("system:app:help:create")
    @Log(title = "应用帮助-新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody AppHelpBo bo) {
        return toAjax(appHelpService.insertByBo(bo));
    }

    /**
     * 应用帮助-修改
     */
    @SaCheckPermission("system:app:help:update")
    @Log(title = "应用帮助-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody AppHelpBo bo) {
        return toAjax(appHelpService.updateByBo(bo));
    }

    /**
     * 应用帮助-修改状态
     */
    @SaCheckPermission("system:app:help:update")
    @Log(title = "应用帮助-修改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated(EditGroup.class) @RequestBody AdminAppHelpStatusUpBo bo) {
        return toAjax(appHelpService.updateAdminStatus(bo));
    }

    /**
     * 应用帮助-删除
     *
     * @param bo 主键串
     */
    @SaCheckPermission("system:app:help:delete")
    @Log(title = "应用帮助-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@Valid DeleteBo bo) {
        return toAjax(appHelpService.deleteWithValidByIds(bo.getIds(), true));
    }
}
