package com.gzhuxn.system.controller.admin.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppSensitiveWordStatusUpBo;
import com.gzhuxn.system.controller.admin.app.vo.AdminAppSensitiveWordPageVo;
import com.gzhuxn.system.domain.bo.AppSensitiveWordBo;
import com.gzhuxn.system.domain.vo.AppSensitiveWordVo;
import com.gzhuxn.system.service.IAppSensitiveWordService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * app应用-敏感词配置
 * 前端访问路由地址为:/personals/sensitiveWord
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/sensitiveWord")
public class AppSensitiveWordController extends BaseController {

    private final IAppSensitiveWordService appSensitiveWordService;

    /**
     * 查询app应用-敏感词配置列表
     */
    @SaCheckPermission("system:app:sensitiveWord:page")
    @GetMapping("/page")
    public TableDataInfo<AdminAppSensitiveWordPageVo> page(AppSensitiveWordBo bo, PageQuery pageQuery) {
        return appSensitiveWordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取app应用-敏感词配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:app:sensitiveWord:detail")
    @GetMapping("/detail")
    public R<AppSensitiveWordVo> detail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(appSensitiveWordService.queryById(id));
    }

    /**
     * 新增app应用-敏感词配置
     */
    @SaCheckPermission("system:app:sensitiveWord:create")
    @Log(title = "敏感词配置-创建", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody AppSensitiveWordBo bo) {
        return toAjax(appSensitiveWordService.insertByBo(bo));
    }

    /**
     * 修改app应用-敏感词配置
     */
    @SaCheckPermission("system:app:sensitiveWord:update")
    @Log(title = "敏感词配置-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody AppSensitiveWordBo bo) {
        return toAjax(appSensitiveWordService.updateByBo(bo));
    }

    /**
     * 修改敏感词状态
     */
    @SaCheckPermission("system:app:sensitiveWord:update")
    @Log(title = "敏感词配置-修改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated(EditGroup.class) @RequestBody AdminAppSensitiveWordStatusUpBo bo) {
        return toAjax(appSensitiveWordService.updateStatus(bo));
    }

    /**
     * 删除app应用-敏感词配置
     */
    @SaCheckPermission("personals:sensitiveWord:delete")
    @Log(title = "敏感词配置-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@Valid DeleteBo bo) {
        return toAjax(appSensitiveWordService.deleteWithValidByIds(bo.getIds(), true));
    }
}
