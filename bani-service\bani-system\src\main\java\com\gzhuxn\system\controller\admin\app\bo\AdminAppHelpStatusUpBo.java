package com.gzhuxn.system.controller.admin.app.bo;

import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * app应用-帮助状态修改
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
public class AdminAppHelpStatusUpBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     */
    @NotNull(message = "状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;
}
