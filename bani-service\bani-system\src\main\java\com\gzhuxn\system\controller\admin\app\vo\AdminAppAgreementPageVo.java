package com.gzhuxn.system.controller.admin.app.vo;

import com.gzhuxn.system.domain.AppAgreement;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * app应用-协议配置视图对象 app_agreement
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@AutoMapper(target = AppAgreement.class, convertGenerate = false)
public class AdminAppAgreementPageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 协议名称
     */
    private String name;

    /**
     * 协议key
     */
    private String agreementKey;

    /**
     * 协议内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     */
    private Long status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
