package com.gzhuxn.system.controller.admin.app.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.system.domain.AppSensitiveWord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * app应用-敏感词配置视图对象 app_sensitive_word
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AppSensitiveWord.class, convertGenerate = false)
public class AdminAppSensitiveWordPageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 0无效、1生效
     */
    @ExcelProperty(value = "0无效、1生效")
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
