package com.gzhuxn.system.domain.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.system.domain.AppSensitiveWord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * app应用-敏感词配置业务对象 app_sensitive_word
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppSensitiveWord.class, reverseConvertGenerate = false)
public class AppSensitiveWordBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer sort;

    /**
     * 0无效、1生效
     */
    @NotNull(message = "0无效、1生效不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;


}
