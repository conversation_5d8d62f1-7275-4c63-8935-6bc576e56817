package com.gzhuxn.system.dubbo;

import cn.hutool.core.text.CharSequenceUtil;
import com.gzhuxn.system.api.RemoteSensitiveWordService;
import com.gzhuxn.system.api.domain.vo.RemoteSensitiveWordVo;
import com.gzhuxn.system.domain.AppSensitiveWord;
import com.gzhuxn.system.service.IAppSensitiveWordService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 敏感词服务实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteSensitiveWordServiceImpl implements RemoteSensitiveWordService {
    private final IAppSensitiveWordService sensitiveWordService;

    @Override
    public List<RemoteSensitiveWordVo> queryAll() {
        List<AppSensitiveWord> sensitiveWords = sensitiveWordService.queryEnableAll();
        if (sensitiveWords.isEmpty()) {
            return List.of();
        }
        return sensitiveWords.stream().map(sensitiveWord -> RemoteSensitiveWordVo.builder()
            .name(sensitiveWord.getName())
            .words(CharSequenceUtil.split(sensitiveWord.getContent(), "\r\n"))
            .build()).toList();
    }
}
