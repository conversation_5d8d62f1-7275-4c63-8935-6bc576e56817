package com.gzhuxn.system.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.core.enums.UserStatus;
import com.gzhuxn.common.core.enums.UserType;
import com.gzhuxn.common.core.exception.ServiceException;
import com.gzhuxn.common.core.exception.user.UserException;
import com.gzhuxn.common.core.utils.DateUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.common.mybatis.helper.DataPermissionHelper;
import com.gzhuxn.common.tenant.helper.TenantHelper;
import com.gzhuxn.system.api.RemoteUserService;
import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import com.gzhuxn.system.api.domain.vo.RemoteUserVo;
import com.gzhuxn.system.api.model.LoginUser;
import com.gzhuxn.system.api.model.RoleDTO;
import com.gzhuxn.system.api.model.XcxLoginUser;
import com.gzhuxn.system.domain.SysUser;
import com.gzhuxn.system.domain.bo.SysUserBo;
import com.gzhuxn.system.domain.vo.SysDeptVo;
import com.gzhuxn.system.domain.vo.SysRoleVo;
import com.gzhuxn.system.domain.vo.SysUserVo;
import com.gzhuxn.system.mapper.SysUserMapper;
import com.gzhuxn.system.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserServiceImpl implements RemoteUserService {

    private final ISysUserService userService;
    private final ISysPermissionService permissionService;
    private final ISysConfigService configService;
    private final ISysRoleService roleService;
    private final ISysDeptService deptService;
    private final SysUserMapper userMapper;

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param tenantId 租户id
     * @return 结果
     */
    @Override
    public LoginUser getUserInfo(String username, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUserVo sysUser = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username));
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", username);
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", username);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(sysUser);
        });
    }

    /**
     * 通过用户id查询用户信息
     *
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 结果
     */
    @Override
    public LoginUser getUserInfo(Long userId, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUserVo sysUser = userMapper.selectVoById(userId);
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", "");
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", sysUser.getUserName());
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(sysUser);
        });
    }

    /**
     * 通过手机号查询用户信息
     *
     * @param phoneNumber 手机号
     * @param tenantId    租户id
     * @return 结果
     */
    @Override
    public LoginUser getUserInfoByPhoneNumber(String phoneNumber, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUserVo sysUser = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhoneNumber, phoneNumber));
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", phoneNumber);
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", phoneNumber);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(sysUser);
        });
    }

    /**
     * 通过邮箱查询用户信息
     *
     * @param email    邮箱
     * @param tenantId 租户id
     * @return 结果
     */
    @Override
    public LoginUser getUserInfoByEmail(String email, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, email));
            if (ObjectUtil.isNull(user)) {
                throw new UserException("user.not.exists", email);
            }
            if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
                throw new UserException("user.blocked", email);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(user);
        });
    }

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    @Override
    public XcxLoginUser getUserInfoByOpenid(String openid) throws UserException {
        SysUser sysUser = userService.selectUserByOpenid(openid);
        if (ObjectUtil.isNull(sysUser)) {
            SysUserBo userBo = new SysUserBo();
            userBo.setOpenId(openid);
            userBo.setUserName("微信用户");
            userBo.setNickName("微信用户");
            userBo.setUserType(UserType.MINI.getUserType());
            userBo.setShortFlag(EnableStatus.ENABLE.getValue());
            userService.registerUser(userBo, TenantHelper.getTenantId());
            sysUser = MapstructUtils.convert(userBo, SysUser.class);
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new UserException("user.blocked", openid);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        XcxLoginUser loginUser = new XcxLoginUser();
        loginUser.setUserId(sysUser.getUserId());
        loginUser.setUsername(sysUser.getUserName());
        loginUser.setNickname(sysUser.getNickName());
        loginUser.setUserType(sysUser.getUserType());
        loginUser.setOpenId(openid);
        loginUser.setShortFlag(sysUser.getShortFlag());
        return loginUser;
    }

    /**
     * 注册用户信息
     *
     * @param remoteUserBo 用户信息
     * @return 结果
     */
    @Override
    public Boolean registerUserInfo(RemoteUserBo remoteUserBo) throws UserException, ServiceException {
        SysUserBo sysUserBo = MapstructUtils.convert(remoteUserBo, SysUserBo.class);
        String username = sysUserBo.getUserName();
        boolean exist = TenantHelper.dynamic(remoteUserBo.getTenantId(), () -> {
            if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
                throw new ServiceException("当前系统没有开启注册功能");
            }
            return userMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, sysUserBo.getUserName()));
        });
        if (exist) {
            throw new UserException("user.register.save.error", username);
        }
        return userService.registerUser(sysUserBo, remoteUserBo.getTenantId());
    }

    @Override
    public Boolean updateUserInfo(RemoteUserBo remoteUserBo) throws UserException, ServiceException {
        SysUser sysUser = userMapper.selectById(remoteUserBo.getUserId());
        Assert.notNull(sysUser, "用户不存在");
        sysUser.setNickName(remoteUserBo.getNickName());
        sysUser.setUserName(remoteUserBo.getUserName());
        sysUser.setAvatar(remoteUserBo.getAvatar());
        sysUser.setSex(remoteUserBo.getSex());
        sysUser.setPhoneNumber(remoteUserBo.getPhoneNumber());
        return userMapper.updateById(sysUser) > 0;
    }

    @Override
    public Boolean updateUserAvatar(RemoteUserBo remoteUserBo) throws UserException, ServiceException {
        SysUser sysUser = userMapper.selectById(remoteUserBo.getUserId());
        Assert.notNull(sysUser, "用户不存在");
        SysUser upSysUser = new SysUser();
        upSysUser.setUserId(sysUser.getUserId());
        upSysUser.setAvatar(remoteUserBo.getAvatar());
        upSysUser.setShortFlag(EnableStatus.DISABLE.getValue());
        return userMapper.updateById(upSysUser) > 0;
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Override
    public String selectUserNameById(Long userId) {
        return userService.selectUserNameById(userId);
    }

    /**
     * 通过用户ID查询用户昵称
     *
     * @param userId 用户ID
     * @return 用户昵称
     */
    @Override
    public String selectNicknameById(Long userId) {
        return userService.selectNicknameById(userId);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userIds 用户ID 多个用逗号隔开
     * @return 用户账户
     */
    @Override
    public String selectNicknameByIds(String userIds) {
        return userService.selectNicknameByIds(userIds);
    }

    /**
     * 通过用户ID查询用户手机号
     *
     * @param userId 用户id
     * @return 用户手机号
     */
    @Override
    public String selectPhoneNumberById(Long userId) {
        return userService.selectPhoneNumberById(userId);
    }

    /**
     * 通过用户ID查询用户邮箱
     *
     * @param userId 用户id
     * @return 用户邮箱
     */
    @Override
    public String selectEmailById(Long userId) {
        return userService.selectEmailById(userId);
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(SysUserVo userVo) {
        LoginUser loginUser = new LoginUser();
        loginUser.setTenantId(userVo.getTenantId());
        loginUser.setUserId(userVo.getUserId());
        loginUser.setDeptId(userVo.getDeptId());
        loginUser.setUsername(userVo.getUserName());
        loginUser.setNickname(userVo.getNickName());
        loginUser.setPassword(userVo.getPassword());
        loginUser.setUserType(userVo.getUserType());
        loginUser.setMenuPermission(permissionService.getMenuPermission(userVo.getUserId()));
        loginUser.setRolePermission(permissionService.getRolePermission(userVo.getUserId()));
        if (ObjectUtil.isNotNull(userVo.getDeptId())) {
            Opt<SysDeptVo> deptOpt = Opt.of(userVo.getDeptId()).map(deptService::selectDeptById);
            loginUser.setDeptName(deptOpt.map(SysDeptVo::getDeptName).orElse(StringUtils.EMPTY));
            loginUser.setDeptCategory(deptOpt.map(SysDeptVo::getDeptCategory).orElse(StringUtils.EMPTY));
        }
        List<SysRoleVo> roles = roleService.selectRolesByUserId(userVo.getUserId());
        loginUser.setRoles(BeanUtil.copyToList(roles, RoleDTO.class));
        return loginUser;
    }

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param ip     IP地址
     */
    @Override
    public void recordLoginInfo(Long userId, String ip) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(ip);
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(userId);
        DataPermissionHelper.ignore(() -> userMapper.updateById(sysUser));
    }

    /**
     * 通过用户ID查询用户列表
     *
     * @param userIds 用户ids
     * @return 用户列表
     */
    @Override
    public List<RemoteUserVo> selectListByIds(List<Long> userIds) {
        List<SysUserVo> sysUserVos = userService.selectUserByIds(userIds, null);
        return MapstructUtils.convert(sysUserVos, RemoteUserVo.class);
    }

    @Override
    public RemoteUserVo selectById(Long userId) {
        SysUserVo sysUserVo = userService.selectUserById(userId);
        return MapstructUtils.convert(sysUserVo, RemoteUserVo.class);
    }

    /**
     * 通过角色ID查询用户ID
     *
     * @param roleIds 角色ids
     * @return 用户ids
     */
    @Override
    public List<Long> selectUserIdsByRoleIds(List<Long> roleIds) {
        return userService.selectUserIdsByRoleIds(roleIds);
    }

    @Override
    public void updateMpSubscribe(String unionId, String mpOpenId, Integer subscribe, LocalDateTime time) {
        SysUser sysUser = userMapper.selectUserByUnionId(unionId);
        if (null == sysUser) {
            // 推送订阅者信息，注册小程序
            return;
        }
        SysUser upUser = new SysUser();
        upUser.setUserId(sysUser.getUserId());
        upUser.setMpOpenId(mpOpenId);
        upUser.setSubscribe(subscribe);
        upUser.setSubscribeTime(time);
        userMapper.updateById(upUser);
    }

    @Override
    public List<RemoteUserVo> selectMpOpenIdsByIds(List<Long> userIds) {
        return userMapper.selectMpOpenIdsByIds(userIds);
    }

    @Override
    public Boolean updateUserStatus(Long userId, String status) {
        return userService.updateUserStatus(userId, status) > 0;
    }

    @Override
    public Boolean logoffUser(Long userId) {
        try {
            // 1. 更新用户状态为删除状态
            int updateResult = userService.updateUserStatus(userId, UserStatus.DELETED.getCode());
            return updateResult > 0;

            // 2. 可以在这里添加其他注销相关的逻辑
            // 例如：清理用户缓存、发送通知等
        } catch (Exception e) {
            // 记录错误日志
            return false;
        }
    }

    @Override
    public Boolean softDeleteUser(Long userId) {
        try {
            // 软删除：将用户状态设置为删除状态，而不是物理删除
            return userService.updateUserStatus(userId, UserStatus.DELETED.getCode()) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Boolean isUserLoggedOff(Long userId) {
        try {
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                // 用户不存在，视为已注销
                return true;
            }

            // 检查用户状态是否为删除状态
            return UserStatus.DELETED.getCode().equals(user.getStatus());
        } catch (Exception e) {
            return false;
        }
    }

}
