package com.gzhuxn.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.system.domain.AppSensitiveWord;
import com.gzhuxn.system.domain.bo.AppSensitiveWordBo;
import com.gzhuxn.system.domain.vo.AppSensitiveWordVo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * app应用-敏感词配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface AppSensitiveWordMapper extends BaseMapperPlus<AppSensitiveWord, AppSensitiveWordVo> {


    /**
     * 构建查询条件
     *
     * @param bo 查询条件
     * @return 查询条件
     */
    default LambdaQueryWrapper<AppSensitiveWord> buildQueryWrapper(AppSensitiveWordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppSensitiveWord> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppSensitiveWord::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AppSensitiveWord::getContent, bo.getContent());
        lqw.eq(bo.getSort() != null, AppSensitiveWord::getSort, bo.getSort());
        lqw.eq(bo.getStatus() != null, AppSensitiveWord::getStatus, bo.getStatus());
        lqw.orderByDesc(AppSensitiveWord::getCreateTime);
        return lqw;
    }

    /**
     * 查询所有启用的敏感词
     *
     * @return List<AppSensitiveWord> 启用的敏感词
     */
    default List<AppSensitiveWord> selectEnableAll() {
        return selectList(new LambdaQueryWrapper<AppSensitiveWord>().eq(AppSensitiveWord::getStatus, EnableStatus.ENABLE.getValue()));
    }
}
