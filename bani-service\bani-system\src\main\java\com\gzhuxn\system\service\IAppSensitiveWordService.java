package com.gzhuxn.system.service;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppSensitiveWordStatusUpBo;
import com.gzhuxn.system.controller.admin.app.vo.AdminAppSensitiveWordPageVo;
import com.gzhuxn.system.domain.AppSensitiveWord;
import com.gzhuxn.system.domain.bo.AppSensitiveWordBo;
import com.gzhuxn.system.domain.vo.AppSensitiveWordVo;

import java.util.Collection;
import java.util.List;

/**
 * app应用-敏感词配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface IAppSensitiveWordService {

    /**
     * 查询app应用-敏感词配置
     *
     * @param id 主键
     * @return app应用-敏感词配置
     */
    AppSensitiveWordVo queryById(Long id);

    /**
     * 分页查询app应用-敏感词配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return app应用-敏感词配置分页列表
     */
    TableDataInfo<AdminAppSensitiveWordPageVo> queryPageList(AppSensitiveWordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的app应用-敏感词配置列表
     *
     * @param bo 查询条件
     * @return app应用-敏感词配置列表
     */
    List<AppSensitiveWordVo> queryList(AppSensitiveWordBo bo);

    /**
     * 查询所有启用的app应用-敏感词配置列表
     *
     * @return app应用-敏感词配置列表
     */
    List<AppSensitiveWord> queryEnableAll();

    /**
     * 新增app应用-敏感词配置
     *
     * @param bo app应用-敏感词配置
     * @return 是否新增成功
     */
    boolean insertByBo(AppSensitiveWordBo bo);

    /**
     * 修改app应用-敏感词配置
     *
     * @param bo app应用-敏感词配置
     * @return 是否修改成功
     */
    boolean updateByBo(AppSensitiveWordBo bo);

    /**
     * 修改app应用-敏感词配置状态
     *
     * @param bo app应用-敏感词配置
     * @return 是否修改成功
     */
    boolean updateStatus(AdminAppSensitiveWordStatusUpBo bo);

    /**
     * 校验并批量删除app应用-敏感词配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
