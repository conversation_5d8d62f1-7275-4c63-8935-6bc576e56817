package com.gzhuxn.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppSensitiveWordStatusUpBo;
import com.gzhuxn.system.controller.admin.app.vo.AdminAppSensitiveWordPageVo;
import com.gzhuxn.system.domain.AppSensitiveWord;
import com.gzhuxn.system.domain.bo.AppSensitiveWordBo;
import com.gzhuxn.system.domain.vo.AppSensitiveWordVo;
import com.gzhuxn.system.mapper.AppSensitiveWordMapper;
import com.gzhuxn.system.service.IAppSensitiveWordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * app应用-敏感词配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@RequiredArgsConstructor
@Service
public class AppSensitiveWordServiceImpl
    extends BaniServiceImpl<AppSensitiveWordMapper, AppSensitiveWord> implements IAppSensitiveWordService {

    /**
     * 查询app应用-敏感词配置
     *
     * @param id 主键
     * @return app应用-敏感词配置
     */
    @Override
    public AppSensitiveWordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询app应用-敏感词配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return app应用-敏感词配置分页列表
     */
    @Override
    public TableDataInfo<AdminAppSensitiveWordPageVo> queryPageList(AppSensitiveWordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppSensitiveWord> lqw = baseMapper.buildQueryWrapper(bo);
        Page<AdminAppSensitiveWordPageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AdminAppSensitiveWordPageVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的app应用-敏感词配置列表
     *
     * @param bo 查询条件
     * @return app应用-敏感词配置列表
     */
    @Override
    public List<AppSensitiveWordVo> queryList(AppSensitiveWordBo bo) {
        LambdaQueryWrapper<AppSensitiveWord> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<AppSensitiveWord> queryEnableAll() {
        return baseMapper.selectEnableAll();
    }

    /**
     * 新增app应用-敏感词配置
     *
     * @param bo app应用-敏感词配置
     * @return 是否新增成功
     */
    @Override
    public boolean insertByBo(AppSensitiveWordBo bo) {
        AppSensitiveWord add = MapstructUtils.convert(bo, AppSensitiveWord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改app应用-敏感词配置
     *
     * @param bo app应用-敏感词配置
     * @return 是否修改成功
     */
    @Override
    public boolean updateByBo(AppSensitiveWordBo bo) {
        AppSensitiveWord update = MapstructUtils.convert(bo, AppSensitiveWord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateStatus(AdminAppSensitiveWordStatusUpBo bo) {
        existById(bo.getId(), "敏感词不存在");
        AppSensitiveWord upSensitiveWord = new AppSensitiveWord();
        upSensitiveWord.setId(bo.getId());
        upSensitiveWord.setStatus(bo.getStatus());
        return updateById(upSensitiveWord);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppSensitiveWord entity) {
    }

    /**
     * 校验并批量删除app应用-敏感词配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }
}
