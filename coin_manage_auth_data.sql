-- 认证成功金币赠送配置数据
INSERT INTO `coin_manage` (`type`, `sub_type`, `name`, `coin`, `create_time`, `update_time`)
VALUES (2, 201, '实名认证奖励', 100, NOW(), NOW()),
       (2, 202, '学历认证奖励', 150, NOW(), NOW()),
       (2, 203, '车辆认证奖励', 120, NOW(), NOW()),
       (2, 204, '房屋认证奖励', 200, NOW(), NOW());

-- 说明：
-- type = 2 表示新手任务类型（NOVICE_TASK）
-- sub_type 对应认证类型：201-实名认证、202-学历认证、203-车辆认证、204-房屋认证
-- coin 表示赠送的金币数量，可以根据实际需求调整

-- 签到任务示例数据（可选）
INSERT INTO `coin_manage` (`type`, `sub_type`, `name`, `coin`, `create_time`, `update_time`)
VALUES (1, 101, '第一天签到', 10, NOW(), NOW()),
       (1, 102, '第二天签到', 15, NOW(), NOW()),
       (1, 103, '第三天签到', 20, NOW(), NOW()),
       (1, 104, '第四天签到', 25, NOW(), NOW()),
       (1, 105, '第五天签到', 30, NOW(), NOW()),
       (1, 106, '第六天签到', 35, NOW(), NOW()),
       (1, 107, '第七天签到', 50, NOW(), NOW());
