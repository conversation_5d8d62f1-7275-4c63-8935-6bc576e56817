# 用户评论异步审核功能实现文档

## 功能概述

实现了用户创建评论时的异步审核功能，包括内容审核、状态管理和审核结果处理。支持动态评论、活动评论等多种类型的评论审核。

## 实现特性

1. **异步审核**: 用户发布评论后，系统异步进行内容审核
2. **状态管理**: 完整的评论审核状态跟踪
3. **自动发布**: 审核通过后自动发布评论
4. **审核拒绝**: 审核不通过时标记为拒绝状态
5. **人工审核**: 异常情况下转人工审核
6. **权限控制**: 用户只能操作自己的评论
7. **内容变更审核**: 修改评论内容时重新审核

## 核心组件

### 1. 审核状态枚举
- `AuditStatus`: 审核状态
  - `WAIT_AUDIT(2, "待审核")`
  - `PASS(3, "通过")`
  - `REJECT(4, "拒绝")`
  - `TRANSFER_WAIT_AUDIT(11, "转人工审核")`

### 2. 审核类型枚举
- `AuditType.COMMENT(4, "评论")`: 评论审核类型

### 3. 评论类型
- `type = 1`: 动态评论
- `type = 2`: 活动评论
- 支持扩展更多评论类型

### 4. 服务层实现

#### UserCommentServiceImpl
- `insertByBo()`: 创建评论时发起审核
- `updateByBo()`: 更新评论时处理审核
- `deleteWithValidByIds()`: 删除评论时权限检查
- `publishComment()`: 发布评论（审核通过）
- `rejectComment()`: 拒绝评论（审核不通过）
- `transferToManualAudit()`: 转人工审核

#### AuditCommentProcessor
- `getAuditType()`: 返回评论审核类型
- `execute()`: 执行评论审核逻辑
- `checkComment()`: 检查评论内容
- `publishComment()`: 发布评论
- `rejectComment()`: 拒绝评论
- `transferToManualAudit()`: 转人工审核

## 业务流程

### 1. 创建评论流程

```
用户创建评论
    ↓
设置当前用户ID
    ↓
设置为待审核状态
    ↓
保存评论
    ↓
发起异步审核事件
```

### 2. 异步审核流程

```
审核事件触发
    ↓
查询评论信息
    ↓
调用内容审核服务
    ↓
审核通过 → 发布评论 → 发送通知
    ↓
审核不通过 → 标记拒绝状态 → 发送通知
    ↓
审核异常 → 转人工审核
```

### 3. 更新评论流程

```
用户更新评论
    ↓
检查权限
    ↓
判断内容是否变化
    ↓
内容变化 → 设置为待审核状态 → 发起异步审核事件
    ↓
内容未变化 → 直接更新
```

### 4. 删除评论流程

```
用户删除评论
    ↓
权限检查（只能删除自己的评论）
    ↓
查询用户关联的评论
    ↓
执行删除操作
```

## 核心代码实现

### 创建评论时发起审核

```java
@Override
@Transactional(rollbackFor = Exception.class)
public Boolean insertByBo(UserCommentBo bo) {
    UserComment add = MapstructUtils.convert(bo, UserComment.class);
    
    // 设置当前用户ID
    add.setCreateBy(LoginHelper.getUserId());
    add.setUserId(LoginHelper.getUserId());
    
    // 设置为待审核状态
    add.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
    
    boolean flag = baseMapper.insert(add) > 0;
    if (flag) {
        bo.setId(add.getId());
        
        // 发起异步审核
        contentAuditRecordService.createAudit(add.getId(), AuditType.COMMENT);
    }
    return flag;
}
```

### 更新评论时处理审核

```java
@Override
@Transactional(rollbackFor = Exception.class)
public Boolean updateByBo(UserCommentBo bo) {
    UserComment update = MapstructUtils.convert(bo, UserComment.class);
    
    // 查询原有评论
    UserComment existing = baseMapper.selectById(bo.getId());
    AssertUtils.notNull(existing, "评论不存在");
    
    // 检查权限：只能修改自己的评论
    AssertUtils.isTrue(existing.getCreateBy().equals(LoginHelper.getUserId()), "无权限修改此评论");
    
    // 如果内容发生变化，需要重新审核
    if (!existing.getContent().equals(bo.getContent())) {
        update.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
    }
    
    boolean flag = baseMapper.updateById(update) > 0;
    
    // 如果内容发生变化，发起异步审核
    if (flag && !existing.getContent().equals(bo.getContent())) {
        contentAuditRecordService.createAudit(bo.getId(), AuditType.COMMENT);
    }
    
    return flag;
}
```

### 评论审核处理器

```java
@Override
public ContentAuditRes execute(Long businessId) {
    log.info("开始审核评论，评论ID={}", businessId);
    
    try {
        // 查询评论信息
        UserComment comment = userCommentService.getById(businessId);
        if (comment == null) {
            log.warn("评论不存在，评论ID={}", businessId);
            return ContentAuditRes.reject("评论不存在");
        }
        
        return checkComment(comment);
    } catch (Exception e) {
        log.error("审核评论失败，评论ID={}，错误信息：{}", businessId, e.getMessage(), e);
        return ContentAuditRes.transferUser();
    }
}

private ContentAuditRes checkComment(UserComment comment) {
    // 使用远程内容审核服务检查文本内容
    RemoteCheckText checkText = remoteContentAuditService.checkText(comment.getContent());
    
    if (checkText.isPass()) {
        // 审核通过，发布评论
        publishComment(comment);
        return ContentAuditRes.isOk();
    } else {
        // 审核不通过，拒绝发布
        String failMsg = checkText.failMag();
        rejectComment(comment);
        return ContentAuditRes.reject(failMsg);
    }
}
```

## 数据库字段

### UserComment 表字段
- `id`: 评论ID
- `content`: 评论内容
- `type`: 评论类型（1-动态评论，2-活动评论）
- `business_id`: 业务ID（动态ID、活动ID等）
- `parent_id`: 父评论ID（回复评论时使用）
- `user_id`: 评论用户ID
- `audit_status`: 审核状态
- `create_by`: 创建者ID
- `create_time`: 创建时间
- `update_time`: 更新时间

## 扩展点

1. **审核规则扩展**: 可在 `AuditCommentProcessor` 中添加更多审核规则
2. **评论类型扩展**: 支持更多业务类型的评论
3. **通知机制**: 可添加审核结果通知功能
4. **审核历史**: 可记录详细的审核历史
5. **批量审核**: 可实现批量审核功能
6. **敏感词过滤**: 可添加本地敏感词过滤
7. **用户信誉度**: 可根据用户信誉度调整审核策略

## 测试验证

提供了完整的测试用例：
- 创建评论并发起审核
- 评论审核处理器测试
- 审核通过和拒绝测试
- 更新评论内容变化审核测试
- 删除评论权限检查测试
- 回复评论审核测试
- 评论类型测试

## 注意事项

1. **权限控制**: 用户只能操作自己的评论
2. **状态一致性**: 确保评论审核状态的一致性
3. **异常处理**: 完善的异常处理和日志记录
4. **性能考虑**: 异步审核避免阻塞用户操作
5. **数据完整性**: 使用事务保证数据一致性
6. **内容变更**: 只有内容变化时才重新审核
7. **回复审核**: 回复评论也需要进行审核

## 使用示例

### 创建动态评论

```java
UserCommentBo commentBo = new UserCommentBo();
commentBo.setContent("这是一条动态评论");
commentBo.setType(1); // 动态评论
commentBo.setBusinessId(123L); // 动态ID
commentBo.setParentId(0L); // 顶级评论

// 创建评论，自动发起审核
boolean result = userCommentService.insertByBo(commentBo);
```

### 创建回复评论

```java
UserCommentBo replyBo = new UserCommentBo();
replyBo.setContent("这是一条回复");
replyBo.setType(1); // 动态评论
replyBo.setBusinessId(123L); // 动态ID
replyBo.setParentId(456L); // 父评论ID

// 创建回复，自动发起审核
boolean result = userCommentService.insertByBo(replyBo);
```

### 查询审核状态

```java
UserComment comment = userCommentService.getById(commentId);
AuditStatus auditStatus = AuditStatus.of(comment.getAuditStatus());
// 根据审核状态进行相应处理
```

这个实现提供了一个完整、高效、可扩展的评论异步审核解决方案，确保评论内容质量的同时提供良好的用户体验。
