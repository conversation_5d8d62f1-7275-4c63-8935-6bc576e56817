# 评论审核功能使用示例

## API 使用示例

### 1. 创建动态评论

```http
POST /user/comment
Content-Type: application/json

{
  "content": "这是一条很棒的动态！",
  "type": 1,
  "businessId": 123,
  "parentId": 0
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

**说明：**
- `type: 1` 表示动态评论
- `businessId` 为动态ID
- `parentId: 0` 表示顶级评论
- 评论创建后自动发起异步审核

### 2. 创建回复评论

```http
POST /user/comment
Content-Type: application/json

{
  "content": "我也觉得很棒！",
  "type": 1,
  "businessId": 123,
  "parentId": 456
}
```

**说明：**
- `parentId: 456` 表示回复ID为456的评论
- 回复评论也需要进行审核

### 3. 创建活动评论

```http
POST /user/comment
Content-Type: application/json

{
  "content": "这个活动很有意思！",
  "type": 2,
  "businessId": 789,
  "parentId": 0
}
```

**说明：**
- `type: 2` 表示活动评论
- `businessId` 为活动ID

### 4. 更新评论内容

```http
PUT /user/comment
Content-Type: application/json

{
  "id": 123,
  "content": "更新后的评论内容"
}
```

**说明：**
- 只有内容发生变化时才会重新审核
- 用户只能修改自己的评论

### 5. 删除评论

```http
DELETE /user/comment/123,456,789
```

**说明：**
- 支持批量删除
- 用户只能删除自己的评论

### 6. 查询评论详情

```http
GET /user/comment/123
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 123,
    "content": "这是一条很棒的动态！",
    "type": 1,
    "businessId": 123,
    "parentId": 0,
    "userId": 456,
    "auditStatus": 3,
    "createTime": "2024-12-02T10:30:00"
  }
}
```

**状态说明：**
- `auditStatus`: 审核状态（2-待审核，3-通过，4-拒绝，11-转人工审核）

## 业务场景示例

### 场景1：正常评论流程

```java
// 1. 用户创建评论
UserCommentBo commentBo = new UserCommentBo();
commentBo.setContent("这个动态很有趣！");
commentBo.setType(1); // 动态评论
commentBo.setBusinessId(123L);
commentBo.setParentId(0L);

// 2. 系统处理
boolean result = userCommentService.insertByBo(commentBo);
// 此时评论状态：待审核

// 3. 异步审核（系统自动执行）
// - 内容审核通过
// - 评论状态：通过
// - 用户收到审核通过通知
```

### 场景2：审核不通过流程

```java
// 1. 用户创建包含违规内容的评论
UserCommentBo commentBo = new UserCommentBo();
commentBo.setContent("包含违规关键词的评论");
commentBo.setType(1);
commentBo.setBusinessId(123L);
commentBo.setParentId(0L);

// 2. 系统处理
boolean result = userCommentService.insertByBo(commentBo);
// 此时评论状态：待审核

// 3. 异步审核（系统自动执行）
// - 内容审核不通过
// - 评论状态：拒绝
// - 用户收到审核拒绝通知
```

### 场景3：回复评论流程

```java
// 1. 用户回复评论
UserCommentBo replyBo = new UserCommentBo();
replyBo.setContent("我也有同样的想法");
replyBo.setType(1);
replyBo.setBusinessId(123L);
replyBo.setParentId(456L); // 回复的父评论ID

// 2. 系统处理
boolean result = userCommentService.insertByBo(replyBo);
// 此时回复状态：待审核

// 3. 异步审核执行...
```

### 场景4：修改评论内容流程

```java
// 1. 用户修改评论内容
UserCommentBo updateBo = new UserCommentBo();
updateBo.setId(123L);
updateBo.setContent("修改后的评论内容");

// 2. 系统处理
boolean result = userCommentService.updateByBo(updateBo);
// 如果内容发生变化，评论状态：待审核

// 3. 异步审核执行...
```

## 审核处理器扩展示例

### 自定义评论审核规则

```java
@Service
public class CustomAuditCommentProcessor implements ContentAuditProcessor {
    
    @Override
    public AuditType getAuditType() {
        return AuditType.COMMENT;
    }
    
    @Override
    public ContentAuditRes execute(Long businessId) {
        UserComment comment = userCommentService.getById(businessId);
        
        // 1. 基础内容审核
        RemoteCheckText checkText = remoteContentAuditService.checkText(comment.getContent());
        if (!checkText.isPass()) {
            return ContentAuditRes.reject(checkText.failMag());
        }
        
        // 2. 自定义规则：检查评论长度
        if (comment.getContent().length() > 200) {
            return ContentAuditRes.reject("评论内容不能超过200字");
        }
        
        // 3. 自定义规则：检查是否包含链接
        if (containsUrl(comment.getContent())) {
            return ContentAuditRes.reject("评论不能包含链接");
        }
        
        // 4. 自定义规则：检查重复评论
        if (isDuplicateComment(comment)) {
            return ContentAuditRes.reject("请勿发布重复评论");
        }
        
        // 5. 自定义规则：用户信誉度检查
        if (isLowCreditUser(comment.getUserId())) {
            return ContentAuditRes.transferUser(); // 转人工审核
        }
        
        // 审核通过
        publishComment(comment);
        return ContentAuditRes.isOk();
    }
    
    private boolean containsUrl(String content) {
        // 检查是否包含URL
        return content.matches(".*https?://.*");
    }
    
    private boolean isDuplicateComment(UserComment comment) {
        // 检查是否为重复评论
        return false;
    }
    
    private boolean isLowCreditUser(Long userId) {
        // 检查用户信誉度
        return false;
    }
}
```

### 评论审核通知处理

```java
@Component
public class CommentAuditNotificationHandler {
    
    @EventListener
    public void handleCommentAuditPass(CommentAuditPassEvent event) {
        // 评论审核通过通知
        sendNotification(event.getUserId(), "您的评论已审核通过");
        
        // 通知被评论的用户
        if (event.getParentId() > 0) {
            UserComment parentComment = userCommentService.getById(event.getParentId());
            sendNotification(parentComment.getUserId(), "您的评论收到了新回复");
        }
    }
    
    @EventListener
    public void handleCommentAuditReject(CommentAuditRejectEvent event) {
        // 评论审核拒绝通知
        sendNotification(event.getUserId(), 
            "您的评论审核未通过，原因：" + event.getReason());
    }
    
    private void sendNotification(Long userId, String message) {
        // 发送通知逻辑
    }
}
```

## 管理端审核功能

### 人工审核接口

```java
@RestController
@RequestMapping("/admin/audit/comment")
public class AdminCommentAuditController {
    
    @PostMapping("/approve/{id}")
    public R<Void> approveComment(@PathVariable Long id) {
        // 管理员审核通过
        contentAuditRecordService.manualApprove(id);
        return R.ok();
    }
    
    @PostMapping("/reject/{id}")
    public R<Void> rejectComment(@PathVariable Long id, @RequestBody String reason) {
        // 管理员审核拒绝
        contentAuditRecordService.manualReject(id, reason);
        return R.ok();
    }
    
    @GetMapping("/pending")
    public TableDataInfo<ContentAuditVo> getPendingAudits(PageQuery pageQuery) {
        // 查询待审核的评论
        return contentAuditRecordService.queryPendingAudits(AuditType.COMMENT, pageQuery);
    }
    
    @GetMapping("/statistics")
    public R<CommentAuditStatistics> getAuditStatistics() {
        // 获取评论审核统计
        return R.ok(commentAuditService.getStatistics());
    }
}
```

### 批量审核功能

```java
@PostMapping("/batch-approve")
public R<Void> batchApproveComments(@RequestBody List<Long> ids) {
    // 批量审核通过
    contentAuditRecordService.batchManualApprove(ids);
    return R.ok();
}

@PostMapping("/batch-reject")
public R<Void> batchRejectComments(@RequestBody BatchRejectRequest request) {
    // 批量审核拒绝
    contentAuditRecordService.batchManualReject(request.getIds(), request.getReason());
    return R.ok();
}
```

## 监控和统计

### 评论审核统计

```java
@Service
public class CommentAuditStatisticsService {
    
    public CommentAuditStatistics getDailyStatistics(LocalDate date) {
        // 获取每日评论审核统计
        return CommentAuditStatistics.builder()
            .totalComments(getTotalComments(date))
            .passCount(getPassCount(date))
            .rejectCount(getRejectCount(date))
            .pendingCount(getPendingCount(date))
            .averageAuditTime(getAverageAuditTime(date))
            .build();
    }
    
    public List<CommentAuditTrend> getAuditTrend(LocalDate startDate, LocalDate endDate) {
        // 获取评论审核趋势数据
        return auditRecordMapper.selectCommentAuditTrend(startDate, endDate);
    }
    
    public Map<String, Long> getRejectReasons(LocalDate date) {
        // 获取拒绝原因统计
        return auditRecordMapper.selectRejectReasons(AuditType.COMMENT, date);
    }
}
```

### 性能监控

```java
@Component
public class CommentAuditMetrics {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void recordCommentAuditTime(CommentAuditCompleteEvent event) {
        // 记录评论审核耗时
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("comment.audit.duration")
            .tag("result", event.getResult())
            .tag("type", event.getCommentType().toString())
            .register(meterRegistry));
    }
    
    @EventListener
    public void recordCommentAuditResult(CommentAuditCompleteEvent event) {
        // 记录评论审核结果
        meterRegistry.counter("comment.audit.result", 
            "type", event.getCommentType().toString(),
            "result", event.getResult()).increment();
    }
}
```

## 配置示例

### 评论审核配置

```yaml
# application.yml
audit:
  comment:
    # 是否启用异步审核
    async-enabled: true
    # 审核超时时间（秒）
    timeout: 30
    # 最大重试次数
    max-retry: 3
    # 评论最大长度
    max-length: 200
    # 是否允许包含链接
    allow-url: false
    # 是否启用重复检查
    duplicate-check: true
```

### 评论规则配置

```java
@ConfigurationProperties(prefix = "audit.comment")
@Data
public class CommentAuditConfig {
    
    /**
     * 是否启用异步审核
     */
    private boolean asyncEnabled = true;
    
    /**
     * 审核超时时间（秒）
     */
    private int timeout = 30;
    
    /**
     * 最大重试次数
     */
    private int maxRetry = 3;
    
    /**
     * 评论最大长度
     */
    private int maxLength = 200;
    
    /**
     * 是否允许包含链接
     */
    private boolean allowUrl = false;
    
    /**
     * 是否启用重复检查
     */
    private boolean duplicateCheck = true;
}
```

## 前端集成示例

### 评论组件

```javascript
// CommentForm.vue
<template>
  <div class="comment-form">
    <textarea 
      v-model="content" 
      placeholder="请输入评论内容..."
      maxlength="200">
    </textarea>
    <button @click="submitComment" :disabled="submitting">
      {{ submitting ? '提交中...' : '发表评论' }}
    </button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      content: '',
      submitting: false
    }
  },
  methods: {
    async submitComment() {
      if (!this.content.trim()) {
        this.$message.warning('请输入评论内容');
        return;
      }
      
      this.submitting = true;
      try {
        await this.$api.createComment({
          content: this.content,
          type: this.commentType,
          businessId: this.businessId,
          parentId: this.parentId || 0
        });
        
        this.$message.success('评论提交成功，正在审核中...');
        this.content = '';
        this.$emit('comment-submitted');
      } catch (error) {
        this.$message.error('评论提交失败');
      } finally {
        this.submitting = false;
      }
    }
  }
}
</script>
```

这个实现提供了完整的评论异步审核功能，包括多种评论类型支持、权限控制、审核状态管理、通知机制等，可以根据具体业务需求进行定制和扩展。
