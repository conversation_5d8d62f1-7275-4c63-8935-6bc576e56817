# 用户动态异步审核功能实现文档

## 功能概述

实现了用户创建动态时的异步审核功能，包括内容审核、状态管理和审核结果处理。

## 实现特性

1. **异步审核**: 用户发布动态后，系统异步进行内容审核
2. **状态管理**: 完整的动态状态和审核状态跟踪
3. **自动发布**: 审核通过后自动发布动态
4. **审核拒绝**: 审核不通过时回到草稿状态
5. **人工审核**: 异常情况下转人工审核

## 核心组件

### 1. 动态状态枚举

- `MomentStatus`: 动态状态
    - `DRAFT(1, "草稿")`
    - `PUBLISHED(2, "已发布")`
    - `DEL(0, "下架")`

### 2. 审核状态枚举

- `AuditStatus`: 审核状态
    - `DRAFT(1, "草稿")`
    - `WAIT_AUDIT(2, "待审核")`
    - `PASS(3, "通过")`
    - `REJECT(4, "拒绝")`
    - `TRANSFER_WAIT_AUDIT(11, "转人工审核")`

### 3. 审核类型枚举

- `AuditType.MOMENT(3, "动态")`: 动态审核类型

### 4. 服务层实现

#### UserMomentServiceImpl

- `insertByBo()`: 创建动态时发起审核
- `updateByBo()`: 更新动态时处理审核
- `publishMoment()`: 发布动态（审核通过）
- `rejectMoment()`: 拒绝动态（审核不通过）
- `transferToManualAudit()`: 转人工审核

#### AuditMomentProcessor

- `getAuditType()`: 返回动态审核类型
- `execute()`: 执行动态审核逻辑
- `checkMoment()`: 检查动态内容
- `publishMoment()`: 发布动态
- `rejectMoment()`: 拒绝动态

### 5. 事件处理

- `ContentAuditProducer`: 发送审核事件
- `ContentAuditConsumer`: 处理审核事件
- `ContentAuditProcessorFactory`: 审核处理器工厂

## 业务流程

### 1. 创建动态流程

```
用户创建动态
    ↓
判断状态
    ↓
草稿状态 → 设置为草稿审核状态 → 保存
    ↓
发布状态 → 设置为待审核状态 → 保存 → 发起异步审核事件
```

### 2. 异步审核流程

```
审核事件触发
    ↓
查询动态信息
    ↓
调用内容审核服务
    ↓
审核通过 → 发布动态 → 发送通知
    ↓
审核不通过 → 回到草稿状态 → 发送通知
    ↓
审核异常 → 转人工审核
```

### 3. 更新动态流程

```
用户更新动态
    ↓
检查权限
    ↓
判断状态变化
    ↓
草稿→发布 → 设置为待审核状态 → 发起异步审核事件
    ↓
其他变化 → 直接更新
```

## 核心代码实现

### 创建动态时发起审核

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean insertByBo(UserMomentBo bo) {
    UserMoment add = MapstructUtils.convert(bo, UserMoment.class);
    
    // 设置当前用户ID
    add.setCreateBy(LoginHelper.getUserId());
    add.setUserId(LoginHelper.getUserId());
    
    // 如果是发布状态，设置为待审核状态
    if (MomentStatus.PUBLISHED.getCode().equals(bo.getStatus())) {
        add.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
    } else {
        // 草稿状态，设置为草稿审核状态
        add.setAuditStatus(AuditStatus.DRAFT.getValue());
    }
    
    boolean flag = baseMapper.insert(add) > 0;
    if (flag) {
        bo.setId(add.getId());
        
        // 如果是发布状态，发起异步审核
        if (MomentStatus.PUBLISHED.getCode().equals(bo.getStatus())) {
            contentAuditRecordService.createAudit(add.getId(), AuditType.MOMENT);
        }
    }
    return flag;
}
```

### 动态审核处理器

```java
@Override
public ContentAuditRes execute(Long businessId) {
    log.info("开始审核动态，动态ID={}", businessId);
    
    try {
        // 查询动态信息
        UserMoment moment = userMomentService.getById(businessId);
        if (moment == null) {
            log.warn("动态不存在，动态ID={}", businessId);
            return ContentAuditRes.reject("动态不存在");
        }
        
        return checkMoment(moment);
    } catch (Exception e) {
        log.error("审核动态失败，动态ID={}，错误信息：{}", businessId, e.getMessage(), e);
        return ContentAuditRes.transferUser();
    }
}

private ContentAuditRes checkMoment(UserMoment moment) {
    // 使用远程内容审核服务检查文本内容
    RemoteCheckText checkText = remoteContentAuditService.checkText(moment.getContent());
    
    if (checkText.isPass()) {
        // 审核通过，发布动态
        publishMoment(moment);
        return ContentAuditRes.isOk();
    } else {
        // 审核不通过，拒绝发布
        String failMsg = checkText.failMag();
        rejectMoment(moment);
        return ContentAuditRes.reject(failMsg);
    }
}
```

## 配置说明

### 审核处理器注册

审核处理器通过 `ContentAuditProcessorConfig` 自动注册：

```java
@Configuration
public class ContentAuditProcessorConfig {
    @Bean
    public ContentAuditProcessorFactory contentAuditProcessorFactory(
        @Lazy @Autowired(required = false) List<ContentAuditProcessor> processors) {
        ContentAuditProcessorFactory processorFactory = new ContentAuditProcessorFactory();
        if (CollUtil.isNotEmpty(processors)) {
            processors.forEach(processorFactory::register);
        }
        return processorFactory;
    }
}
```

## 扩展点

1. **审核规则扩展**: 可在 `AuditMomentProcessor` 中添加更多审核规则
2. **通知机制**: 可添加审核结果通知功能
3. **审核历史**: 可记录详细的审核历史
4. **批量审核**: 可实现批量审核功能
5. **审核统计**: 可添加审核统计和报表功能

## 测试验证

提供了完整的测试用例：

- 创建动态并发起审核
- 动态审核处理器测试
- 审核通过和拒绝测试
- 草稿状态不发起审核测试
- 状态变更审核测试

## 注意事项

1. **权限控制**: 用户只能操作自己的动态
2. **状态一致性**: 确保动态状态和审核状态的一致性
3. **异常处理**: 完善的异常处理和日志记录
4. **性能考虑**: 异步审核避免阻塞用户操作
5. **数据完整性**: 使用事务保证数据一致性

## 使用示例

### 创建动态

```java
UserMomentBo momentBo = new UserMomentBo();
momentBo.setContent("这是一条动态内容");
momentBo.setImages("image1.jpg,image2.jpg");
momentBo.setStatus(MomentStatus.PUBLISHED.getCode()); // 发布状态

// 创建动态，自动发起审核
boolean result = userMomentService.insertByBo(momentBo);
```

### 查询审核状态

```java
UserMoment moment = userMomentService.getById(momentId);
AuditStatus auditStatus = AuditStatus.of(moment.getAuditStatus());
// 根据审核状态进行相应处理
```

这个实现提供了一个完整、高效、可扩展的动态异步审核解决方案，确保内容质量的同时提供良好的用户体验。
