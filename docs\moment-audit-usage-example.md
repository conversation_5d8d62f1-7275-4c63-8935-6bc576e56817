# 动态审核功能使用示例

## API 使用示例

### 1. 创建动态（发布状态）

```http
POST /user/moment
Content-Type: application/json

{
  "content": "今天天气真好，出去走走！",
  "images": "image1.jpg,image2.jpg",
  "status": 2
}
```

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

**说明：**

- `status: 2` 表示发布状态，系统会自动发起异步审核
- 动态创建后状态为"待审核"，审核通过后自动发布

### 2. 创建动态（草稿状态）

```http
POST /user/moment
Content-Type: application/json

{
  "content": "这是一条草稿动态",
  "images": "image1.jpg",
  "status": 1
}
```

**说明：**

- `status: 1` 表示草稿状态，不会发起审核
- 用户可以后续编辑并发布

### 3. 更新动态（草稿改为发布）

```http
PUT /user/moment
Content-Type: application/json

{
  "id": 123,
  "content": "更新后的动态内容",
  "images": "image1.jpg,image2.jpg",
  "status": 2
}
```

**说明：**

- 从草稿状态改为发布状态会自动发起审核
- 其他状态变更不会重新审核

### 4. 查询动态详情

```http
GET /user/moment/123
```

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 123,
    "content": "今天天气真好，出去走走！",
    "images": "image1.jpg,image2.jpg",
    "status": 2,
    "auditStatus": 3,
    "createTime": "2024-12-02T10:30:00"
  }
}
```

**状态说明：**

- `status`: 动态状态（1-草稿，2-已发布，0-下架）
- `auditStatus`: 审核状态（1-草稿，2-待审核，3-通过，4-拒绝，11-转人工审核）

## 业务场景示例

### 场景1：正常发布流程

```java
// 1. 用户创建动态
UserMomentBo momentBo = new UserMomentBo();
momentBo.setContent("分享一张美丽的风景照片");
momentBo.setImages("landscape.jpg");
momentBo.setStatus(MomentStatus.PUBLISHED.getCode());

// 2. 系统处理
boolean result = userMomentService.insertByBo(momentBo);
// 此时动态状态：已发布，审核状态：待审核

// 3. 异步审核（系统自动执行）
// - 内容审核通过
// - 动态状态：已发布，审核状态：通过
// - 用户收到审核通过通知
```

### 场景2：审核不通过流程

```java
// 1. 用户创建包含违规内容的动态
UserMomentBo momentBo = new UserMomentBo();
momentBo.setContent("包含违规关键词的内容");
momentBo.setStatus(MomentStatus.PUBLISHED.getCode());

// 2. 系统处理
boolean result = userMomentService.insertByBo(momentBo);
// 此时动态状态：已发布，审核状态：待审核

// 3. 异步审核（系统自动执行）
// - 内容审核不通过
// - 动态状态：草稿，审核状态：拒绝
// - 用户收到审核拒绝通知，可重新编辑
```

### 场景3：草稿编辑发布流程

```java
// 1. 用户先创建草稿
UserMomentBo draftBo = new UserMomentBo();
draftBo.setContent("草稿内容");
draftBo.setStatus(MomentStatus.DRAFT.getCode());
userMomentService.insertByBo(draftBo);
// 此时动态状态：草稿，审核状态：草稿

// 2. 用户编辑并发布
UserMomentBo publishBo = new UserMomentBo();
publishBo.setId(draftBo.getId());
publishBo.setContent("编辑后的内容");
publishBo.setStatus(MomentStatus.PUBLISHED.getCode());
userMomentService.updateByBo(publishBo);
// 此时动态状态：已发布，审核状态：待审核

// 3. 异步审核执行...
```

## 审核处理器扩展示例

### 自定义审核规则

```java
@Service
public class CustomAuditMomentProcessor implements ContentAuditProcessor {
    
    @Override
    public AuditType getAuditType() {
        return AuditType.MOMENT;
    }
    
    @Override
    public ContentAuditRes execute(Long businessId) {
        UserMoment moment = userMomentService.getById(businessId);
        
        // 1. 基础内容审核
        RemoteCheckText checkText = remoteContentAuditService.checkText(moment.getContent());
        if (!checkText.isPass()) {
            return ContentAuditRes.reject(checkText.failMag());
        }
        
        // 2. 自定义规则：检查图片数量
        if (StringUtils.isNotBlank(moment.getImages())) {
            String[] images = moment.getImages().split(",");
            if (images.length > 6) {
                return ContentAuditRes.reject("图片数量不能超过6张");
            }
        }
        
        // 3. 自定义规则：检查内容长度
        if (moment.getContent().length() > 500) {
            return ContentAuditRes.reject("动态内容不能超过500字");
        }
        
        // 4. 自定义规则：敏感词检查
        if (containsSensitiveWords(moment.getContent())) {
            return ContentAuditRes.reject("内容包含敏感词汇");
        }
        
        // 审核通过
        publishMoment(moment);
        return ContentAuditRes.isOk();
    }
    
    private boolean containsSensitiveWords(String content) {
        // 实现敏感词检查逻辑
        return false;
    }
}
```

### 审核结果通知

```java
@Component
public class MomentAuditNotificationHandler {
    
    @EventListener
    public void handleAuditPass(MomentAuditPassEvent event) {
        // 审核通过通知
        sendNotification(event.getUserId(), "您的动态已审核通过并发布");
    }
    
    @EventListener
    public void handleAuditReject(MomentAuditRejectEvent event) {
        // 审核拒绝通知
        sendNotification(event.getUserId(), 
            "您的动态审核未通过，原因：" + event.getReason());
    }
    
    private void sendNotification(Long userId, String message) {
        // 发送通知逻辑
    }
}
```

## 管理端审核功能

### 人工审核接口

```java
@RestController
@RequestMapping("/admin/audit/moment")
public class AdminMomentAuditController {
    
    @PostMapping("/approve/{id}")
    public R<Void> approveMoment(@PathVariable Long id) {
        // 管理员审核通过
        contentAuditRecordService.manualApprove(id);
        return R.ok();
    }
    
    @PostMapping("/reject/{id}")
    public R<Void> rejectMoment(@PathVariable Long id, @RequestBody String reason) {
        // 管理员审核拒绝
        contentAuditRecordService.manualReject(id, reason);
        return R.ok();
    }
    
    @GetMapping("/pending")
    public TableDataInfo<ContentAuditVo> getPendingAudits(PageQuery pageQuery) {
        // 查询待审核的动态
        return contentAuditRecordService.queryPendingAudits(AuditType.MOMENT, pageQuery);
    }
}
```

## 监控和统计

### 审核统计

```java
@Service
public class MomentAuditStatisticsService {
    
    public AuditStatistics getDailyStatistics(LocalDate date) {
        // 获取每日审核统计
        return AuditStatistics.builder()
            .totalAudits(getTotalAudits(date))
            .passCount(getPassCount(date))
            .rejectCount(getRejectCount(date))
            .pendingCount(getPendingCount(date))
            .build();
    }
    
    public List<AuditTrend> getAuditTrend(LocalDate startDate, LocalDate endDate) {
        // 获取审核趋势数据
        return auditRecordMapper.selectAuditTrend(startDate, endDate);
    }
}
```

### 性能监控

```java
@Component
public class MomentAuditMetrics {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void recordAuditTime(MomentAuditCompleteEvent event) {
        // 记录审核耗时
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("moment.audit.duration")
            .tag("result", event.getResult())
            .register(meterRegistry));
    }
    
    @EventListener
    public void recordAuditResult(MomentAuditCompleteEvent event) {
        // 记录审核结果
        meterRegistry.counter("moment.audit.result", 
            "type", event.getResult()).increment();
    }
}
```

## 配置示例

### 审核配置

```yaml
# application.yml
audit:
  moment:
    # 是否启用异步审核
    async-enabled: true
    # 审核超时时间（秒）
    timeout: 30
    # 最大重试次数
    max-retry: 3
    # 是否启用人工审核
    manual-audit-enabled: true
```

### 审核规则配置

```java
@ConfigurationProperties(prefix = "audit.moment")
@Data
public class MomentAuditConfig {
    
    /**
     * 是否启用异步审核
     */
    private boolean asyncEnabled = true;
    
    /**
     * 审核超时时间（秒）
     */
    private int timeout = 30;
    
    /**
     * 最大重试次数
     */
    private int maxRetry = 3;
    
    /**
     * 内容最大长度
     */
    private int maxContentLength = 500;
    
    /**
     * 最大图片数量
     */
    private int maxImageCount = 6;
}
```

这个实现提供了完整的动态审核功能，包括自动审核、人工审核、通知机制、统计监控等，可以根据具体业务需求进行定制和扩展。
