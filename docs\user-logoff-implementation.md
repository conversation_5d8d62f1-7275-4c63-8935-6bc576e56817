# 用户注销功能实现文档

## 功能概述

实现了用户注销逻辑，支持用户发起注销申请后通过延时事件任务实现用户注销。

## 实现特性

1. **延时注销**: 用户申请注销后，系统会在7天后自动执行注销操作
2. **可取消**: 在延时期间，用户可以取消注销申请
3. **事件驱动**: 使用延时事件任务处理注销逻辑
4. **状态管理**: 完整的注销状态跟踪（注销中、注销成功）

## 核心组件

### 1. 枚举类

- `UserLogoffStatus`: 用户注销状态枚举
    - `PROCESSING(0, "注销中")`
    - `SUCCESS(1, "注销成功")`

### 2. 事件处理

- `UserLogoffProducer`: 用户注销事件生产者
- `UserLogoffConsumer`: 用户注销事件消费者

### 3. 控制器

- `AppUserLogoffRecordController`: 用户端注销控制器
    - `POST /user/logoffRecord/apply`: 申请注销
    - `POST /user/logoffRecord/cancel/{id}`: 取消注销
    - `GET /user/logoffRecord/current`: 查询当前用户注销记录

- `UserLogoffRecordController`: 管理端注销控制器
    - `GET /user/logoffRecord/page`: 分页查询
    - `POST /user/logoffRecord/create`: 创建记录
    - `POST /user/logoffRecord/update`: 更新记录
    - `POST /user/logoffRecord/delete`: 删除记录

### 4. 服务层

- `IUserLogoffRecordService`: 服务接口
    - `applyUserLogoff()`: 申请用户注销
    - `cancelUserLogoff(Long id)`: 取消用户注销
    - `processUserLogoff(Long logoffRecordId, Long userId)`: 处理用户注销

## 配置参数

在 `PersonalsConstant` 中添加了注销延时时间配置：

```java
/**
 * 用户注销延时时间，单位：秒
 * 默认7天
 */
long USER_LOGOFF_DELAY_TIME = 60L * 60 * 24 * 7;
```

## 业务流程

1. **申请注销**
    - 用户调用申请注销接口
    - 系统创建注销记录，状态为"注销中"
    - 发送延时事件（7天后执行）

2. **取消注销**
    - 用户可在延时期间取消注销
    - 系统删除注销记录
    - 延时事件自动失效

3. **执行注销**
    - 延时事件触发
    - 更新注销记录状态为"注销成功"
    - 强制注销用户所有会话
    - 执行用户数据清理（可扩展）

## 安全特性

1. **权限控制**: 用户只能操作自己的注销记录
2. **重复申请检查**: 防止用户重复申请注销
3. **状态校验**: 严格的状态流转控制
4. **事务保证**: 关键操作使用事务保证数据一致性

## 扩展点

1. **数据清理**: 在 `processUserLogoff` 方法中可添加更多用户数据清理逻辑
2. **通知机制**: 可添加邮件、短信等通知功能
3. **审核流程**: 可添加管理员审核环节
4. **延时时间**: 可配置化延时时间设置

## 测试

提供了基础的单元测试，验证枚举和实体类的正确性。

## 注意事项

1. 确保延时事件系统正常运行
2. 注销操作不可逆，需要用户确认
3. 建议在生产环境中添加更多的数据备份和恢复机制
