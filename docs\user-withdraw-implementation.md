# 用户提现功能实现文档

## 功能概述

实现了完整的用户花瓣提现功能，包括提现申请、审核流程、汇款管理等，严格按照金币提现机制规则执行。

## 提现规则

### 金币提现机制（仅针对充值金币）

| 规则描述 | 描述 |
|---------|------|
| 兑换比例 | 1元人民币 = 10花瓣金币 |
| 提现比例 | 70%（如充值100币，最多提现70币 = 7元） |
| 提现手续费 | 5%（实际到账66.5币 = 6.65元） |
| 提现门槛 | ≥50元等值金币（即500花瓣金币） |
| 提现频率 | 每日≤1次，单日上限200元等值金币（即2000花瓣金币） |
| 实名验证 | 必须完成实名认证方可提现 |

## 核心组件

### 1. 枚举类型

#### WithdrawAuditStatus（提现审核状态）
```java
public enum WithdrawAuditStatus {
    APPLY(1, "发起申请"),
    PASS(2, "审核通过"),
    RETURN(3, "打回补充"),
    REJECT(4, "拒绝");
}
```

#### WithdrawRemitStatus（提现汇款状态）
```java
public enum WithdrawRemitStatus {
    WAIT_REMIT(1, "待汇款"),
    REMITTED(2, "已汇款");
}
```

### 2. 业务对象

#### UserWithdrawCreateBo（提现申请创建）
```java
public class UserWithdrawCreateBo {
    @NotNull @DecimalMin("50.0")
    private BigDecimal amount;              // 提现金额（元）
    
    @NotNull
    private Long withdrawQrCodeImage;       // 收款二维码图片ID
}
```

#### UserWithdrawAuditBo（提现审核）
```java
public class UserWithdrawAuditBo {
    @NotNull
    private Long id;                        // 提现申请ID
    
    @NotNull
    private Integer auditStatus;            // 审核状态
    
    private String auditRemark;             // 审核备注
}
```

#### UserWithdrawRemitBo（提现汇款）
```java
public class UserWithdrawRemitBo {
    @NotNull
    private Long id;                        // 提现申请ID
    
    @NotNull
    private Integer remitStatus;            // 汇款状态
    
    private String remitRemark;             // 汇款备注
}
```

### 3. 服务层实现

#### IUserWithdrawService 接口
- `createWithdrawApply()` - 发起提现申请
- `auditWithdraw()` - 审核提现申请
- `remitWithdraw()` - 提现汇款

## 业务流程

### 提现申请流程

```
用户发起提现申请
    ↓
验证提现规则
    ↓
计算需要扣除的花瓣数量
    ↓
锁定用户花瓣
    ↓
创建提现申请记录
    ↓
等待管理员审核
```

### 审核流程

```
管理员审核提现申请
    ↓
审核通过 → 等待汇款
    ↓
审核拒绝 → 解锁花瓣
    ↓
打回补充 → 用户可重新提交
```

### 汇款流程

```
审核通过的申请
    ↓
管理员执行汇款
    ↓
标记汇款完成
    ↓
实际扣除用户花瓣
```

## 核心算法

### 1. 花瓣数量计算

```java
/**
 * 计算需要扣除的花瓣数量
 * 规则：1元 = 10花瓣，提现比例70%，手续费5%
 * 实际需要的花瓣 = 提现金额 / 0.7 / 0.95 * 10
 */
private int calculateRequiredCoin(BigDecimal amount) {
    // 提现金额 / 70% / 95% * 10 = 提现金额 * 10 / 0.665
    BigDecimal requiredCoin = amount.multiply(new BigDecimal("10"))
        .divide(new BigDecimal("0.665"), 0, RoundingMode.UP);
    return requiredCoin.intValue();
}
```

### 2. 可提现金额计算

```java
/**
 * 计算可提现金额
 * 规则：1元人民币 = 10花瓣金币，提现比例70%，手续费5%
 */
private BigDecimal calculateWithdrawableAmount(Integer withdrawCoin) {
    if (withdrawCoin == null || withdrawCoin <= 0) {
        return BigDecimal.ZERO;
    }
    
    // 1元 = 10花瓣，所以花瓣转换为元需要除以10
    BigDecimal coinToYuan = new BigDecimal(withdrawCoin).divide(new BigDecimal("10"), 2, RoundingMode.DOWN);
    
    // 提现比例70%
    BigDecimal withdrawableBase = coinToYuan.multiply(new BigDecimal("0.7"));
    
    // 扣除手续费5%，实际到账95%
    BigDecimal actualAmount = withdrawableBase.multiply(new BigDecimal("0.95"));
    
    return actualAmount.setScale(2, RoundingMode.DOWN);
}
```

## API 接口

### 1. 用户端接口

#### 发起提现申请
```http
POST /withdraw/create
Content-Type: application/json
Authorization: Bearer {token}

{
  "amount": 100.00,
  "withdrawQrCodeImage": 123
}
```

#### 查询账户详情（包含可提现金额）
```http
GET /account/detail
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "userId": 12345,
    "coin": 5000,
    "withdrawCoin": 3000,
    "withdrawableAmount": 199.50
  }
}
```

### 2. 管理端接口

#### 审核提现申请
```http
POST /admin/withdraw/audit
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "id": 1,
  "auditStatus": 2,
  "auditRemark": "审核通过"
}
```

#### 提现汇款
```http
POST /admin/withdraw/remit
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "id": 1,
  "remitStatus": 2,
  "remitRemark": "汇款完成"
}
```

## 验证规则

### 1. 提现申请验证

```java
private void validateWithdrawRules(Long userId, BigDecimal amount) {
    // 1. 验证提现门槛：≥50元
    AssertUtils.isTrue(amount.compareTo(new BigDecimal("50")) >= 0, "提现金额不能少于50元");
    
    // 2. 验证单日上限：≤200元
    AssertUtils.isTrue(amount.compareTo(new BigDecimal("200")) <= 0, "单日提现金额不能超过200元");
    
    // 3. 验证今日是否已提现
    LocalDate today = LocalDate.now();
    long todayCount = baseMapper.countTodayWithdraw(userId, today);
    AssertUtils.isTrue(todayCount == 0, "每日只能提现一次");
    
    // 4. 验证实名认证
    // AssertUtils.isTrue(userDetailService.isRealNameVerified(userId), "必须完成实名认证方可提现");
    
    // 5. 验证可提现花瓣是否足够
    int requiredCoin = calculateRequiredCoin(amount);
    // 验证逻辑...
}
```

### 2. 审核状态验证

```java
// 只有"发起申请"状态的申请才能审核
WithdrawAuditStatus currentStatus = WithdrawAuditStatus.of(withdraw.getAuditStatus());
AssertUtils.isTrue(currentStatus == WithdrawAuditStatus.APPLY, "当前状态不允许审核");
```

### 3. 汇款状态验证

```java
// 只有审核通过的申请才能汇款
WithdrawAuditStatus auditStatus = WithdrawAuditStatus.of(withdraw.getAuditStatus());
AssertUtils.isTrue(auditStatus == WithdrawAuditStatus.PASS, "只有审核通过的申请才能汇款");

// 只有待汇款状态才能执行汇款
WithdrawRemitStatus currentRemitStatus = WithdrawRemitStatus.of(withdraw.getRemitStatus());
AssertUtils.isTrue(currentRemitStatus == WithdrawRemitStatus.WAIT_REMIT, "当前状态不允许汇款");
```

## 数据库设计

### user_withdraw 表结构
```sql
CREATE TABLE user_withdraw (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '提现用户ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '提现金额',
    withdraw_qr_code_image BIGINT NOT NULL COMMENT '收款二维码图片ID',
    audit_status INT NOT NULL DEFAULT 1 COMMENT '审核状态;1发起申请、2审核通过、3打回补充、4拒绝',
    audit_time DATETIME COMMENT '审核时间',
    audit_user_id BIGINT COMMENT '审核人',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    remit_status INT NOT NULL DEFAULT 1 COMMENT '汇款状态;1待汇款、2已汇款',
    remit_time DATETIME COMMENT '汇款时间',
    remit_remark VARCHAR(500) COMMENT '汇款备注',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_audit_status (audit_status),
    INDEX idx_remit_status (remit_status),
    INDEX idx_create_time (create_time)
);
```

## Mapper Default 方法

### UserWithdrawMapper 优化

```java
/**
 * 查询用户今日提现次数
 */
default long countTodayWithdraw(Long userId, LocalDate today) {
    return selectCount(Wrappers.<UserWithdraw>lambdaQuery()
        .eq(UserWithdraw::getUserId, userId)
        .apply("DATE(create_time) = {0}", today));
}

/**
 * 查询用户待审核的提现申请
 */
default List<UserWithdraw> selectPendingWithdraws(Long userId) {
    return selectList(Wrappers.<UserWithdraw>lambdaQuery()
        .eq(UserWithdraw::getUserId, userId)
        .eq(UserWithdraw::getAuditStatus, 1)
        .orderByDesc(UserWithdraw::getCreateTime));
}

/**
 * 查询指定状态的提现申请
 */
default List<UserWithdraw> selectByAuditStatus(Integer auditStatus) {
    return selectList(Wrappers.<UserWithdraw>lambdaQuery()
        .eq(UserWithdraw::getAuditStatus, auditStatus)
        .orderByAsc(UserWithdraw::getCreateTime));
}
```

## 扩展功能

### 1. 提现统计

```java
// 提现统计信息
public class WithdrawStatistics {
    private Long totalApplications;     // 总申请数
    private BigDecimal totalAmount;     // 总提现金额
    private Long pendingCount;          // 待审核数量
    private Long approvedCount;         // 已审核通过数量
    private Long rejectedCount;         // 已拒绝数量
}
```

### 2. 提现限制

```java
// 用户提现限制检查
public class WithdrawLimitChecker {
    public boolean checkDailyLimit(Long userId);           // 检查每日限制
    public boolean checkMonthlyLimit(Long userId);         // 检查每月限制
    public boolean checkRealNameVerification(Long userId); // 检查实名认证
}
```

### 3. 提现通知

```java
// 提现状态变更通知
@EventListener
public void handleWithdrawStatusChange(WithdrawStatusChangeEvent event) {
    // 发送通知给用户
    notificationService.sendWithdrawNotification(event.getUserId(), event.getStatus());
}
```

## 注意事项

### 1. 数据一致性
- 使用事务确保花瓣锁定和提现申请的一致性
- 审核拒绝时要正确解锁花瓣
- 汇款完成时要实际扣除花瓣

### 2. 安全性
- 验证用户权限，只能操作自己的提现申请
- 管理员操作要有权限验证
- 防止重复提交和并发问题

### 3. 性能考虑
- 使用索引优化查询性能
- 对于频繁查询的数据考虑缓存
- 大量数据时使用分页查询

### 4. 监控和日志
- 记录所有关键操作的日志
- 监控提现申请的处理时效
- 异常情况的告警机制

这个实现提供了完整的用户提现功能，严格按照业务规则执行，确保了资金安全和数据一致性。
