-- 用户实名认证支付记录表
CREATE TABLE `user_auth_pay_record`
(
    `id`              bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`         bigint(20)     NOT NULL COMMENT '用户ID',
    `auth_apply_id`   bigint(20)     NOT NULL COMMENT '认证申请ID',
    `auth_type`       int(11)        NOT NULL COMMENT '认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证',
    `original_amount` decimal(10, 2)          DEFAULT NULL COMMENT '原费用',
    `amount`          decimal(10, 2) NOT NULL COMMENT '实际支付金额',
    `coin`            int(11)                 DEFAULT NULL COMMENT '兑换花瓣数量',
    `pay_status`      int(11)        NOT NULL DEFAULT '1' COMMENT '支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功',
    `order_id`        bigint(20)              DEFAULT NULL COMMENT '支付订单ID',
    `use_status`      int(11)        NOT NULL DEFAULT '0' COMMENT '使用状态;0未使用、1已使用',
    `pay_time`        datetime                DEFAULT NULL COMMENT '支付时间',
    `create_time`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_auth_apply_id` (`auth_apply_id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_pay_status` (`pay_status`),
    KEY `idx_use_status` (`use_status`),
    KEY `idx_user_auth_type` (`user_id`, `auth_type`, `use_status`, `pay_status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户-实名认证支付记录';
